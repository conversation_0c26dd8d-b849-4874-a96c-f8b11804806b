"""
Information Extraction Microservice
Handles XML parsing and CUFE value extraction
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.responses import JSONResponse
import uvicorn
import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from lxml import etree
import re

from shared.schemas.extraction import (
    ExtractionRequest,
    ExtractionResponse,
    CUFEData,
    BatchExtractionRequest,
    BatchExtractionResponse,
    BatchExtractionResult,
    MeasurementUnit
)
from shared.database.connection import get_db
from shared.utils.logger import get_logger
from shared.database.models import CUFERecord, ExtractedFileRecord
from shared.services.llm_extraction_service import LLMExtractionService

# Initialize FastAPI app
app = FastAPI(
    title="Extraction Service",
    description="Microservice for extracting CUFE values from XML files",
    version="1.0.0"
)

logger = get_logger(__name__)

# Initialize LLM extraction service
llm_service = LLMExtractionService()

# UBL namespaces for Colombian electronic invoices
UBL_NAMESPACES = {
    'cbc': 'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2',
    'cac': 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2',
    'ext': 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2',
    'sts': 'dian:gov:co:facturaelectronica:Structures-2-1',
    'invoice': 'urn:oasis:names:specification:ubl:schema:xsd:Invoice-2'
}

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "extraction-service"}

# Extraction endpoints
@app.post("/extract-cufe", response_model=ExtractionResponse)
async def extract_cufe(
    request: ExtractionRequest,
    db: Session = Depends(get_db)
):
    """
    Extract CUFE value from XML file
    """
    try:
        logger.info(f"Extracting CUFE from XML file: {request.xml_file_path}")

        # Validate XML file exists
        if not os.path.exists(request.xml_file_path):
            raise HTTPException(status_code=404, detail=f"XML file not found: {request.xml_file_path}")

        # Parse XML and extract CUFE
        cufe_data = _extract_cufe_from_xml(request.xml_file_path, request.extract_additional_data)

        if not cufe_data.cufe_value:
            logger.warning(f"No CUFE found in XML file: {request.xml_file_path}")
            return ExtractionResponse(
                success=False,
                cufe_value="",
                xml_file_path=request.xml_file_path,
                message="No CUFE found in XML file",
                errors=["CUFE element not found in XML structure"]
            )

        # Store CUFE record in database if email_id is provided
        if request.email_id:
            try:
                _store_cufe_record(cufe_data, request.xml_file_path, request.email_id, db)
                db.commit()
                logger.info(f"CUFE record stored in database: {cufe_data.cufe_value}")
            except Exception as e:
                logger.error(f"Failed to store CUFE record: {str(e)}")
                db.rollback()
                # Continue with response even if database storage fails

        return ExtractionResponse(
            success=True,
            cufe_value=cufe_data.cufe_value,
            xml_file_path=request.xml_file_path,
            message="CUFE extraction completed successfully",
            cufe_data=cufe_data,
            extraction_date=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting CUFE: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CUFE extraction failed: {str(e)}")

def _extract_cufe_from_xml(xml_file_path: str, extract_additional_data: bool = True) -> CUFEData:
    """
    Extract CUFE and additional data from Colombian UBL XML invoice using LLM with XPath fallback
    """
    try:
        # First, try LLM-based extraction
        if extract_additional_data:
            logger.info(f"Attempting LLM-based extraction for: {xml_file_path}")

            # Read XML content
            with open(xml_file_path, 'r', encoding='utf-8') as xml_file:
                xml_content = xml_file.read()

            # Try LLM extraction
            llm_result = llm_service.extract_invoice_data(xml_content)

            if llm_result and llm_result.cufe_value:
                logger.info(f"LLM extraction successful for CUFE: {llm_result.cufe_value[:20]}...")
                return llm_result
            else:
                logger.warning("LLM extraction failed or returned no CUFE, falling back to XPath method")

        # Fallback to traditional XPath extraction
        logger.info(f"Using XPath extraction for: {xml_file_path}")
        return _extract_cufe_from_xml_xpath(xml_file_path, extract_additional_data)

    except Exception as e:
        logger.error(f"Error in CUFE extraction: {str(e)}")
        # Try XPath as final fallback
        try:
            logger.info("Attempting XPath fallback after error")
            return _extract_cufe_from_xml_xpath(xml_file_path, extract_additional_data)
        except Exception as fallback_error:
            logger.error(f"XPath fallback also failed: {str(fallback_error)}")
            raise

def _extract_cufe_from_xml_xpath(xml_file_path: str, extract_additional_data: bool = True) -> CUFEData:
    """
    Traditional XPath-based extraction method (fallback)
    """
    try:
        # Parse XML file
        with open(xml_file_path, 'rb') as xml_file:
            tree = etree.parse(xml_file)
            root = tree.getroot()

        # Extract CUFE from cbc:UUID element
        cufe_value = ""
        uuid_elements = root.xpath('//cbc:UUID[@schemeName="CUFE"]', namespaces=UBL_NAMESPACES)

        if uuid_elements:
            cufe_value = uuid_elements[0].text.strip() if uuid_elements[0].text else ""
        else:
            # Try alternative xpath patterns
            uuid_elements = root.xpath('//cbc:UUID', namespaces=UBL_NAMESPACES)
            for uuid_elem in uuid_elements:
                scheme_name = uuid_elem.get('schemeName', '')
                if scheme_name.upper() == 'CUFE':
                    cufe_value = uuid_elem.text.strip() if uuid_elem.text else ""
                    break

            # If still no CUFE found, try simple CUFE element (for non-UBL XML)
            if not cufe_value:
                # Try without namespace first
                simple_cufe_elements = root.xpath('//CUFE')
                if simple_cufe_elements:
                    cufe_value = simple_cufe_elements[0].text.strip() if simple_cufe_elements[0].text else ""
                else:
                    # Try with any namespace (using local-name())
                    simple_cufe_elements = root.xpath('//*[local-name()="CUFE"]')
                    if simple_cufe_elements:
                        cufe_value = simple_cufe_elements[0].text.strip() if simple_cufe_elements[0].text else ""

        # Initialize all extraction variables
        additional_fields = {}
        issuer_name = None
        document_number = None
        issue_date = None
        total_amount = None

        # Enhanced invoice data
        tax_exclusive_amount = None
        tax_inclusive_amount = None
        allowance_total_amount = None
        charge_total_amount = None
        prepaid_amount = None
        payable_amount = None
        total_tax_amount = None
        iva_amount = None
        rete_fuente_amount = None
        rete_iva_amount = None
        rete_ica_amount = None
        due_date = None
        currency_code = None
        invoice_type_code = None
        accounting_cost = None

        line_items = []
        allowance_charges = []
        payment_terms = []

        if extract_additional_data and cufe_value:
            # Extract basic invoice information
            issuer_name = _extract_text_from_xpath(root, '//cac:AccountingSupplierParty/cac:Party/cac:PartyName/cbc:Name')
            document_number = _extract_text_from_xpath(root, '//cbc:ID')

            # Extract dates
            issue_date = _extract_date_from_xpath(root, '//cbc:IssueDate')
            due_date = _extract_date_from_xpath(root, '//cbc:DueDate')

            # Extract currency and invoice type
            currency_code = _extract_text_from_xpath(root, '//cbc:DocumentCurrencyCode')
            invoice_type_code = _extract_text_from_xpath(root, '//cbc:InvoiceTypeCode')
            accounting_cost = _extract_text_from_xpath(root, '//cbc:AccountingCost')

            # Extract monetary totals from LegalMonetaryTotal
            tax_exclusive_amount = _extract_text_from_xpath(root, '//cac:LegalMonetaryTotal/cbc:TaxExclusiveAmount')
            tax_inclusive_amount = _extract_text_from_xpath(root, '//cac:LegalMonetaryTotal/cbc:TaxInclusiveAmount')
            allowance_total_amount = _extract_text_from_xpath(root, '//cac:LegalMonetaryTotal/cbc:AllowanceTotalAmount')
            charge_total_amount = _extract_text_from_xpath(root, '//cac:LegalMonetaryTotal/cbc:ChargeTotalAmount')
            prepaid_amount = _extract_text_from_xpath(root, '//cac:LegalMonetaryTotal/cbc:PrepaidAmount')
            payable_amount = _extract_text_from_xpath(root, '//cac:LegalMonetaryTotal/cbc:PayableAmount')

            # Use tax_inclusive_amount as total_amount for backward compatibility
            total_amount = tax_inclusive_amount

            # Extract tax breakdown
            total_tax_amount = _extract_text_from_xpath(root, '//cac:TaxTotal/cbc:TaxAmount')

            # Extract specific tax types (IVA, retentions)
            iva_amount, rete_fuente_amount, rete_iva_amount, rete_ica_amount = _extract_tax_details(root)

            # Extract line items
            line_items = _extract_line_items(root)

            # Extract allowances and charges
            allowance_charges = _extract_allowance_charges(root)

            # Extract payment terms
            payment_terms = _extract_payment_terms(root)

            # Extract additional fields for debugging
            additional_fields = {
                'xml_root_tag': root.tag,
                'namespaces_found': list(root.nsmap.keys()) if hasattr(root, 'nsmap') else [],
                'uuid_elements_count': len(uuid_elements),
                'line_items_count': len(line_items),
                'allowance_charges_count': len(allowance_charges),
                'payment_terms_count': len(payment_terms)
            }

        return CUFEData(
            cufe_value=cufe_value,
            issuer_name=issuer_name,
            document_number=document_number,
            issue_date=issue_date,
            total_amount=total_amount,
            tax_exclusive_amount=tax_exclusive_amount,
            tax_inclusive_amount=tax_inclusive_amount,
            allowance_total_amount=allowance_total_amount,
            charge_total_amount=charge_total_amount,
            prepaid_amount=prepaid_amount,
            payable_amount=payable_amount,
            total_tax_amount=total_tax_amount,
            iva_amount=iva_amount,
            rete_fuente_amount=rete_fuente_amount,
            rete_iva_amount=rete_iva_amount,
            rete_ica_amount=rete_ica_amount,
            due_date=due_date,
            currency_code=currency_code,
            invoice_type_code=invoice_type_code,
            accounting_cost=accounting_cost,
            line_items=line_items,
            allowance_charges=allowance_charges,
            payment_terms=payment_terms,
            additional_fields=additional_fields
        )

    except etree.XMLSyntaxError as e:
        logger.error(f"XML parsing error for {xml_file_path}: {str(e)}")
        raise ValueError(f"Invalid XML file: {str(e)}")
    except Exception as e:
        logger.error(f"Error extracting CUFE from {xml_file_path}: {str(e)}")
        raise

def _extract_text_from_xpath(root, xpath: str) -> Optional[str]:
    """Helper function to extract text from XPath expression"""
    try:
        elements = root.xpath(xpath, namespaces=UBL_NAMESPACES)
        if elements and elements[0].text:
            return elements[0].text.strip()
    except Exception as e:
        logger.debug(f"Error extracting text from xpath {xpath}: {str(e)}")
    return None

def _extract_date_from_xpath(root, xpath: str) -> Optional[datetime]:
    """Helper function to extract date from XPath expression"""
    try:
        date_str = _extract_text_from_xpath(root, xpath)
        if date_str:
            return datetime.strptime(date_str, '%Y-%m-%d')
    except (ValueError, AttributeError) as e:
        logger.debug(f"Could not parse date from xpath {xpath}: {str(e)}")
    return None

def _extract_tax_details(root) -> tuple:
    """Extract specific tax amounts (IVA, retentions)"""
    iva_amount = None
    rete_fuente_amount = None
    rete_iva_amount = None
    rete_ica_amount = None

    try:
        # Extract tax subtotals to identify different tax types
        tax_subtotals = root.xpath('//cac:TaxTotal/cac:TaxSubtotal', namespaces=UBL_NAMESPACES)

        for subtotal in tax_subtotals:
            tax_amount_elem = subtotal.xpath('./cbc:TaxAmount', namespaces=UBL_NAMESPACES)
            tax_category_elem = subtotal.xpath('./cac:TaxCategory/cbc:ID', namespaces=UBL_NAMESPACES)
            tax_scheme_elem = subtotal.xpath('./cac:TaxCategory/cac:TaxScheme/cbc:ID', namespaces=UBL_NAMESPACES)

            if tax_amount_elem and tax_amount_elem[0].text:
                amount = tax_amount_elem[0].text.strip()

                # Identify tax type based on category and scheme
                category_id = tax_category_elem[0].text.strip() if tax_category_elem and tax_category_elem[0].text else ""
                scheme_id = tax_scheme_elem[0].text.strip() if tax_scheme_elem and tax_scheme_elem[0].text else ""

                # Colombian tax identification patterns
                if scheme_id == "01" or "IVA" in scheme_id.upper():  # IVA
                    iva_amount = amount
                elif scheme_id == "06" or "FUENTE" in scheme_id.upper():  # Retención en la fuente
                    rete_fuente_amount = amount
                elif scheme_id == "05" or "RETEIVA" in scheme_id.upper():  # Retención de IVA
                    rete_iva_amount = amount
                elif scheme_id == "07" or "ICA" in scheme_id.upper():  # Retención de ICA
                    rete_ica_amount = amount

    except Exception as e:
        logger.debug(f"Error extracting tax details: {str(e)}")

    return iva_amount, rete_fuente_amount, rete_iva_amount, rete_ica_amount

def _convert_unit_to_enum(unit_str: str) -> Optional[MeasurementUnit]:
    """Convert string unit of measure to MeasurementUnit enum"""
    if not unit_str:
        return None

    unit_str = unit_str.lower().strip()

    # Common unit mappings
    unit_mappings = {
        'kg': MeasurementUnit.KILOGRAM,
        'kilogram': MeasurementUnit.KILOGRAM,
        'g': MeasurementUnit.GRAM,
        'gram': MeasurementUnit.GRAM,
        'l': MeasurementUnit.LITER,
        'liter': MeasurementUnit.LITER,
        'litre': MeasurementUnit.LITER,
        'ml': MeasurementUnit.MILLILITER,
        'milliliter': MeasurementUnit.MILLILITER,
        'm': MeasurementUnit.METER,
        'meter': MeasurementUnit.METER,
        'cm': MeasurementUnit.CENTIMETER,
        'centimeter': MeasurementUnit.CENTIMETER,
        'unit': MeasurementUnit.UNIT,
        'units': MeasurementUnit.UNIT,
        'pcs': MeasurementUnit.PIECE,
        'piece': MeasurementUnit.PIECE,
        'pieces': MeasurementUnit.PIECE,
        'box': MeasurementUnit.BOX,
        'bottle': MeasurementUnit.BOTTLE,
        'hr': MeasurementUnit.HOUR,
        'hour': MeasurementUnit.HOUR,
        'hours': MeasurementUnit.HOUR,
        'day': MeasurementUnit.DAY,
        'days': MeasurementUnit.DAY,
        '%': MeasurementUnit.PERCENTAGE,
        'percent': MeasurementUnit.PERCENTAGE,
        'percentage': MeasurementUnit.PERCENTAGE,
    }

    return unit_mappings.get(unit_str, MeasurementUnit.UNKNOWN)

def _extract_line_items(root) -> List:
    """Extract invoice line items"""
    from shared.schemas.extraction import InvoiceLineItemData

    line_items = []
    try:
        invoice_lines = root.xpath('//cac:InvoiceLine', namespaces=UBL_NAMESPACES)

        for line in invoice_lines:
            line_number_elem = line.xpath('./cbc:ID', namespaces=UBL_NAMESPACES)
            line_number = int(line_number_elem[0].text.strip()) if line_number_elem and line_number_elem[0].text else None

            # Item details
            item_name = _extract_text_from_xpath(line, './cac:Item/cbc:Name')
            item_description = _extract_text_from_xpath(line, './cac:Item/cbc:Description')
            item_code = _extract_text_from_xpath(line, './cac:Item/cac:SellersItemIdentification/cbc:ID')

            # Quantities and measures
            invoiced_quantity = _extract_text_from_xpath(line, './cbc:InvoicedQuantity')
            unit_of_measure_elem = line.xpath('./cbc:InvoicedQuantity/@unitCode', namespaces=UBL_NAMESPACES)
            unit_of_measure_str = unit_of_measure_elem[0] if unit_of_measure_elem else None
            unit_of_measure = _convert_unit_to_enum(unit_of_measure_str)

            # Pricing
            unit_price = _extract_text_from_xpath(line, './cac:Price/cbc:PriceAmount')
            line_extension_amount = _extract_text_from_xpath(line, './cbc:LineExtensionAmount')

            # Calculate subtotal without VAT (same as line_extension_amount in most cases)
            subtotal_without_vat = line_extension_amount

            # Tax information for the line
            line_tax_amount = _extract_text_from_xpath(line, './cac:TaxTotal/cbc:TaxAmount')

            # Extract VAT rate and additional tax details
            vat_rate = None
            tax_category_code = None
            tax_exemption_reason = None
            vat_amount_per_unit = None

            tax_subtotal = line.xpath('./cac:TaxTotal/cac:TaxSubtotal', namespaces=UBL_NAMESPACES)
            if tax_subtotal:
                vat_rate = _extract_text_from_xpath(tax_subtotal[0], './cac:TaxCategory/cbc:Percent')
                tax_category_code = _extract_text_from_xpath(tax_subtotal[0], './cac:TaxCategory/cbc:ID')
                tax_exemption_reason = _extract_text_from_xpath(tax_subtotal[0], './cac:TaxCategory/cbc:TaxExemptionReason')

                # Calculate VAT per unit if possible
                if line_tax_amount and invoiced_quantity:
                    try:
                        tax_total = float(line_tax_amount)
                        quantity = float(invoiced_quantity)
                        if quantity > 0:
                            vat_amount_per_unit = str(tax_total / quantity)
                    except (ValueError, TypeError):
                        pass

            # Calculate tax inclusive amount if available
            line_tax_inclusive_amount = None
            if line_extension_amount and line_tax_amount:
                try:
                    base = float(line_extension_amount)
                    tax = float(line_tax_amount)
                    line_tax_inclusive_amount = str(base + tax)
                except (ValueError, TypeError):
                    pass

            # Allowances/charges at line level
            allowance_charge_amount = _extract_text_from_xpath(line, './cac:AllowanceCharge/cbc:Amount')

            # Free of charge indicator
            free_of_charge_elem = line.xpath('./cac:Item/cbc:FreeOfChargeIndicator', namespaces=UBL_NAMESPACES)
            free_of_charge_indicator = False
            if free_of_charge_elem and free_of_charge_elem[0].text:
                free_of_charge_indicator = free_of_charge_elem[0].text.strip().lower() == 'true'

            line_item = InvoiceLineItemData(
                line_number=line_number,
                item_name=item_name,
                item_description=item_description,
                item_code=item_code,
                invoiced_quantity=invoiced_quantity,
                unit_of_measure=unit_of_measure,
                unit_price=unit_price,
                subtotal_without_vat=subtotal_without_vat,
                line_extension_amount=line_extension_amount,
                line_tax_amount=line_tax_amount,
                line_tax_inclusive_amount=line_tax_inclusive_amount,
                allowance_charge_amount=allowance_charge_amount,
                free_of_charge_indicator=free_of_charge_indicator,
                vat_rate=vat_rate,
                vat_amount_per_unit=vat_amount_per_unit,
                tax_category_code=tax_category_code,
                tax_exemption_reason=tax_exemption_reason
            )

            line_items.append(line_item)

    except Exception as e:
        logger.debug(f"Error extracting line items: {str(e)}")

    return line_items

def _extract_allowance_charges(root) -> List:
    """Extract invoice-level allowances and charges"""
    from shared.schemas.extraction import InvoiceAllowanceChargeData

    allowance_charges = []
    try:
        allowance_charge_elements = root.xpath('//cac:AllowanceCharge', namespaces=UBL_NAMESPACES)

        for ac_elem in allowance_charge_elements:
            # Skip line-level allowances/charges (already handled in line items)
            if ac_elem.getparent().tag.endswith('InvoiceLine'):
                continue

            charge_indicator_elem = ac_elem.xpath('./cbc:ChargeIndicator', namespaces=UBL_NAMESPACES)
            charge_indicator = False
            if charge_indicator_elem and charge_indicator_elem[0].text:
                charge_indicator = charge_indicator_elem[0].text.strip().lower() == 'true'

            allowance_charge_reason_code = _extract_text_from_xpath(ac_elem, './cbc:AllowanceChargeReasonCode')
            allowance_charge_reason = _extract_text_from_xpath(ac_elem, './cbc:AllowanceChargeReason')
            multiplier_factor_numeric = _extract_text_from_xpath(ac_elem, './cbc:MultiplierFactorNumeric')
            amount = _extract_text_from_xpath(ac_elem, './cbc:Amount')
            base_amount = _extract_text_from_xpath(ac_elem, './cbc:BaseAmount')

            # Tax information for allowance/charge
            tax_category = _extract_text_from_xpath(ac_elem, './cac:TaxCategory/cbc:ID')
            tax_amount = _extract_text_from_xpath(ac_elem, './cac:TaxCategory/cbc:TaxAmount')

            allowance_charge = InvoiceAllowanceChargeData(
                charge_indicator=charge_indicator,
                allowance_charge_reason_code=allowance_charge_reason_code,
                allowance_charge_reason=allowance_charge_reason,
                multiplier_factor_numeric=multiplier_factor_numeric,
                amount=amount,
                base_amount=base_amount,
                tax_category=tax_category,
                tax_amount=tax_amount
            )

            allowance_charges.append(allowance_charge)

    except Exception as e:
        logger.debug(f"Error extracting allowance charges: {str(e)}")

    return allowance_charges

def _extract_payment_terms(root) -> List:
    """Extract payment terms and conditions"""
    from shared.schemas.extraction import InvoicePaymentTermData

    payment_terms = []
    try:
        payment_terms_elements = root.xpath('//cac:PaymentTerms', namespaces=UBL_NAMESPACES)
        payment_means_elements = root.xpath('//cac:PaymentMeans', namespaces=UBL_NAMESPACES)

        # Extract from PaymentTerms
        for pt_elem in payment_terms_elements:
            payment_due_date = _extract_date_from_xpath(pt_elem, './cbc:PaymentDueDate')
            payment_terms_note = _extract_text_from_xpath(pt_elem, './cbc:Note')

            # Settlement period
            settlement_period_measure = _extract_text_from_xpath(pt_elem, './cac:SettlementPeriod/cbc:DurationMeasure')
            settlement_period_unit_elem = pt_elem.xpath('./cac:SettlementPeriod/cbc:DurationMeasure/@unitCode', namespaces=UBL_NAMESPACES)
            settlement_period_unit = settlement_period_unit_elem[0] if settlement_period_unit_elem else None

            # Discount and penalty
            settlement_discount_percent = _extract_text_from_xpath(pt_elem, './cac:SettlementDiscountPercent')
            penalty_surcharge_percent = _extract_text_from_xpath(pt_elem, './cac:PenaltySurchargePercent')
            amount = _extract_text_from_xpath(pt_elem, './cbc:Amount')

            payment_term = InvoicePaymentTermData(
                payment_due_date=payment_due_date,
                payment_terms_note=payment_terms_note,
                settlement_period_measure=settlement_period_measure,
                settlement_period_unit=settlement_period_unit,
                settlement_discount_percent=settlement_discount_percent,
                penalty_surcharge_percent=penalty_surcharge_percent,
                amount=amount
            )

            payment_terms.append(payment_term)

        # Extract from PaymentMeans
        for pm_elem in payment_means_elements:
            payment_means_code = _extract_text_from_xpath(pm_elem, './cbc:PaymentMeansCode')

            if payment_means_code:
                payment_term = InvoicePaymentTermData(
                    payment_means_code=payment_means_code
                )
                payment_terms.append(payment_term)

    except Exception as e:
        logger.debug(f"Error extracting payment terms: {str(e)}")

    return payment_terms

def _store_cufe_record(cufe_data: CUFEData, xml_file_path: str, email_id: str, db: Session):
    """
    Store CUFE record in database
    """
    # Find the XML file record
    xml_file_record = db.query(ExtractedFileRecord).filter(
        ExtractedFileRecord.file_path == xml_file_path,
        ExtractedFileRecord.file_type == "xml"
    ).first()

    if not xml_file_record:
        logger.warning(f"XML file record not found for {xml_file_path}")
        return

    # Check if CUFE already exists
    existing_cufe = db.query(CUFERecord).filter(
        CUFERecord.cufe_value == cufe_data.cufe_value
    ).first()

    if existing_cufe:
        logger.info(f"CUFE already exists in database: {cufe_data.cufe_value}")
        return existing_cufe

    # Create new CUFE record with comprehensive invoice data
    cufe_record = CUFERecord(
        cufe_value=cufe_data.cufe_value,
        email_record_id=xml_file_record.zip_file_record.email_record.id if xml_file_record.zip_file_record and xml_file_record.zip_file_record.email_record else None,
        xml_file_record_id=xml_file_record.id,

        # Basic invoice information
        issuer_name=cufe_data.issuer_name,
        document_number=cufe_data.document_number,
        issue_date=cufe_data.issue_date,
        total_amount=cufe_data.total_amount,

        # Enhanced tax and monetary details
        tax_exclusive_amount=cufe_data.tax_exclusive_amount,
        tax_inclusive_amount=cufe_data.tax_inclusive_amount,
        allowance_total_amount=cufe_data.allowance_total_amount,
        charge_total_amount=cufe_data.charge_total_amount,
        prepaid_amount=cufe_data.prepaid_amount,
        payable_amount=cufe_data.payable_amount,

        # Tax breakdown details
        total_tax_amount=cufe_data.total_tax_amount,
        iva_amount=cufe_data.iva_amount,
        rete_fuente_amount=cufe_data.rete_fuente_amount,
        rete_iva_amount=cufe_data.rete_iva_amount,
        rete_ica_amount=cufe_data.rete_ica_amount,

        # Additional invoice details
        due_date=cufe_data.due_date,
        currency_code=cufe_data.currency_code,
        invoice_type_code=cufe_data.invoice_type_code,
        accounting_cost=cufe_data.accounting_cost,

        extraction_date=datetime.now()
    )

    db.add(cufe_record)
    db.flush()  # Flush to get the ID

    # Store line items
    if cufe_data.line_items:
        from shared.database.models import InvoiceLineItem
        for line_item_data in cufe_data.line_items:
            line_item = InvoiceLineItem(
                cufe_record_id=cufe_record.id,
                line_number=line_item_data.line_number,
                item_name=line_item_data.item_name,
                item_description=line_item_data.item_description,
                item_code=line_item_data.item_code,
                invoiced_quantity=line_item_data.invoiced_quantity,
                unit_of_measure=line_item_data.unit_of_measure,
                unit_price=line_item_data.unit_price,
                line_extension_amount=line_item_data.line_extension_amount,
                line_tax_amount=line_item_data.line_tax_amount,
                line_tax_inclusive_amount=line_item_data.line_tax_inclusive_amount,
                allowance_charge_amount=line_item_data.allowance_charge_amount,
                free_of_charge_indicator=line_item_data.free_of_charge_indicator
            )
            db.add(line_item)

    # Store allowance charges
    if cufe_data.allowance_charges:
        from shared.database.models import InvoiceAllowanceCharge
        for ac_data in cufe_data.allowance_charges:
            allowance_charge = InvoiceAllowanceCharge(
                cufe_record_id=cufe_record.id,
                charge_indicator=ac_data.charge_indicator,
                allowance_charge_reason_code=ac_data.allowance_charge_reason_code,
                allowance_charge_reason=ac_data.allowance_charge_reason,
                multiplier_factor_numeric=ac_data.multiplier_factor_numeric,
                amount=ac_data.amount,
                base_amount=ac_data.base_amount,
                tax_category=ac_data.tax_category,
                tax_amount=ac_data.tax_amount
            )
            db.add(allowance_charge)

    # Store payment terms
    if cufe_data.payment_terms:
        from shared.database.models import InvoicePaymentTerm
        for pt_data in cufe_data.payment_terms:
            payment_term = InvoicePaymentTerm(
                cufe_record_id=cufe_record.id,
                payment_means_code=pt_data.payment_means_code,
                payment_due_date=pt_data.payment_due_date,
                payment_terms_note=pt_data.payment_terms_note,
                settlement_period_measure=pt_data.settlement_period_measure,
                settlement_period_unit=pt_data.settlement_period_unit,
                settlement_discount_percent=pt_data.settlement_discount_percent,
                penalty_surcharge_percent=pt_data.penalty_surcharge_percent,
                amount=pt_data.amount
            )
            db.add(payment_term)

    return cufe_record

@app.post("/batch-extract", response_model=BatchExtractionResponse)
async def batch_extract_cufe(
    request: BatchExtractionRequest,
    db: Session = Depends(get_db)
):
    """
    Extract CUFE values from multiple XML files
    """
    try:
        logger.info(f"Batch extracting CUFE from {len(request.xml_file_paths)} XML files")

        results = []
        successful_extractions = 0
        failed_extractions = 0

        for i, xml_file_path in enumerate(request.xml_file_paths):
            try:
                # Extract CUFE from XML file
                cufe_data = _extract_cufe_from_xml(xml_file_path, True)

                if cufe_data.cufe_value:
                    # Store in database if email_id is provided
                    email_id = request.email_ids[i] if request.email_ids and i < len(request.email_ids) else None
                    if email_id:
                        try:
                            _store_cufe_record(cufe_data, xml_file_path, email_id, db)
                        except Exception as e:
                            logger.warning(f"Failed to store CUFE record for {xml_file_path}: {str(e)}")

                    results.append(BatchExtractionResult(
                        xml_file_path=xml_file_path,
                        success=True,
                        cufe_value=cufe_data.cufe_value,
                        cufe_data=cufe_data
                    ))
                    successful_extractions += 1
                else:
                    results.append(BatchExtractionResult(
                        xml_file_path=xml_file_path,
                        success=False,
                        error_message="No CUFE found in XML file"
                    ))
                    failed_extractions += 1

            except Exception as e:
                logger.error(f"Error processing {xml_file_path}: {str(e)}")
                results.append(BatchExtractionResult(
                    xml_file_path=xml_file_path,
                    success=False,
                    error_message=str(e)
                ))
                failed_extractions += 1

        # Commit database changes
        try:
            db.commit()
        except Exception as e:
            logger.error(f"Database commit failed: {str(e)}")
            db.rollback()

        return BatchExtractionResponse(
            success=failed_extractions == 0,
            message=f"Batch CUFE extraction completed. {successful_extractions} successful, {failed_extractions} failed.",
            processed_count=len(request.xml_file_paths),
            successful_extractions=successful_extractions,
            failed_extractions=failed_extractions,
            results=results
        )

    except Exception as e:
        logger.error(f"Error in batch CUFE extraction: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch extraction failed: {str(e)}")

@app.get("/cufe/{cufe_value}")
async def get_cufe_info(cufe_value: str, db: Session = Depends(get_db)):
    """Get CUFE information by CUFE value"""
    try:
        cufe_record = db.query(CUFERecord).filter(
            CUFERecord.cufe_value == cufe_value
        ).first()

        if not cufe_record:
            raise HTTPException(status_code=404, detail=f"CUFE not found: {cufe_value}")

        return {
            "cufe_value": cufe_record.cufe_value,
            "issuer_name": cufe_record.issuer_name,
            "document_number": cufe_record.document_number,
            "issue_date": cufe_record.issue_date,
            "total_amount": cufe_record.total_amount,
            "extraction_date": cufe_record.extraction_date,
            "xml_file_path": cufe_record.xml_file_record.file_path if cufe_record.xml_file_record else None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving CUFE info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve CUFE info: {str(e)}")

@app.get("/status")
async def get_extraction_status():
    """Get current extraction status"""
    return {
        "service": "extraction-service",
        "status": "running",
        "supported_formats": ["UBL 2.1 XML"],
        "supported_namespaces": list(UBL_NAMESPACES.keys()),
        "cufe_xpath": "//cbc:UUID[@schemeName='CUFE']"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8003)),
        reload=os.getenv("DEBUG", "false").lower() == "true"
    )
