import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { cufeApi, CUFERecord, fileProcessingApi, excelApi } from '../services/api'
import LoadingSpinner from '../components/LoadingSpinner'

const DashboardPage: React.FC = () => {
  const { user, client, logout } = useAuth()
  const [cufeRecords, setCufeRecords] = useState<CUFERecord[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')
  const [totalRecords, setTotalRecords] = useState(0)
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingMessage, setProcessingMessage] = useState<string>('')
  const [showEmailForm, setShowEmailForm] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [expandedInvoices, setExpandedInvoices] = useState<Set<string>>(new Set())
  const [selectedProvider, setSelectedProvider] = useState('Gmail')
  const [emailConfig, setEmailConfig] = useState({
    email_host: 'imap.gmail.com',
    email_port: 993,
    email_username: '',
    email_password: '',
    use_ssl: true,
    folder: 'INBOX',
    max_emails: 10
  })

  const emailProviders = [
    { name: 'Gmail', host: 'imap.gmail.com', port: 993, ssl: true },
    { name: 'Outlook', host: 'outlook.office365.com', port: 993, ssl: true },
    { name: 'Yahoo', host: 'imap.mail.yahoo.com', port: 993, ssl: true },
    { name: 'Custom', host: '', port: 993, ssl: true }
  ]

  useEffect(() => {
    loadCufeRecords()
  }, [])

  const loadCufeRecords = async () => {
    try {
      setLoading(true)
      setError('')
      const response = await cufeApi.getCUFERecords(0, 50)
      setCufeRecords(response.records)
      setTotalRecords(response.total)
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load invoice records')
    } finally {
      setLoading(false)
    }
  }

  const handleProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const providerName = e.target.value
    setSelectedProvider(providerName)

    const provider = emailProviders.find(p => p.name === providerName)
    if (provider && provider.host) {
      setEmailConfig(prev => ({
        ...prev,
        email_host: provider.host,
        email_port: provider.port,
        use_ssl: provider.ssl
      }))
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatAmount = (amount?: string) => {
    if (!amount) return 'N/A'
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP'
    }).format(parseFloat(amount))
  }

  const getProductCount = (record: CUFERecord) => {
    return record.line_items?.length || 0
  }

  const formatProductCount = (count: number) => {
    if (count === 0) return 'Sin productos'
    if (count === 1) return '1 producto'
    return `${count} productos`
  }



  const handleEmailProcessing = async () => {
    if (!emailConfig.email_username || !emailConfig.email_password) {
      setError('Please provide email credentials')
      return
    }

    try {
      setIsProcessing(true)
      setError('')
      setProcessingMessage('Connecting to email server...')

      const result = await fileProcessingApi.processEmails(emailConfig)

      setProcessingMessage(`Processing completed successfully! Processed ${result.processed_count || 0} emails.`)

      // Reload the records to show new data
      await loadCufeRecords()

      setShowEmailForm(false)

    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to process emails')
    } finally {
      setIsProcessing(false)
      setProcessingMessage('')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setEmailConfig(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked :
              type === 'number' ? parseInt(value) || 0 : value
    }))
  }

  const handleExportToExcel = async () => {
    try {
      setIsExporting(true)
      setError('')

      // Call the export API
      const blob = await excelApi.exportInvoices(0, 1000)

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
      link.download = `facturas_export_${timestamp}.xlsx`

      // Trigger download
      document.body.appendChild(link)
      link.click()

      // Cleanup
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to export to Excel')
    } finally {
      setIsExporting(false)
    }
  }

  const toggleInvoiceExpansion = (cufeValue: string) => {
    setExpandedInvoices(prev => {
      const newSet = new Set(prev)
      if (newSet.has(cufeValue)) {
        newSet.delete(cufeValue)
      } else {
        newSet.add(cufeValue)
      }
      return newSet
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Invoice Dashboard</h1>
              <p className="text-sm text-gray-600 mt-1">
                Welcome back, {user?.full_name || user?.username}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{client?.company_name}</p>
                <p className="text-xs text-gray-500">Client ID: {client?.client_id}</p>
              </div>
              <button
                onClick={handleExportToExcel}
                className="btn-success"
                disabled={isExporting || totalRecords === 0}
                title={totalRecords === 0 ? "No invoices to export" : "Export invoices to Excel"}
              >
                {isExporting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Exporting...
                  </>
                ) : (
                  <>📊 Export to Excel</>
                )}
              </button>
              <button
                onClick={() => setShowEmailForm(!showEmailForm)}
                className="btn-primary"
                disabled={isProcessing}
              >
                📧 Process Emails
              </button>
              <button
                onClick={logout}
                className="btn-secondary"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Stats */}
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                      <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3h4v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Invoices</p>
                  <p className="text-2xl font-semibold text-gray-900">{totalRecords}</p>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Processed</p>
                  <p className="text-2xl font-semibold text-gray-900">{cufeRecords.length}</p>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                      <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Client</p>
                  <p className="text-lg font-semibold text-gray-900">{client?.client_id}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Email Processing Section */}
          {showEmailForm && (
            <div className="card mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Process Email Invoices</h2>
              <p className="text-gray-600 mb-6">
                Enter your email credentials to automatically download and process invoice attachments.
                The system will search for ZIP files containing invoices and extract CUFE codes.
              </p>

              {isProcessing ? (
                <div className="flex flex-col items-center py-8">
                  <LoadingSpinner size="lg" />
                  <p className="mt-4 text-sm text-gray-600">{processingMessage}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Email Provider Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Provider
                    </label>
                    <select
                      value={selectedProvider}
                      onChange={handleProviderChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      {emailProviders.map(provider => (
                        <option key={provider.name} value={provider.name}>
                          {provider.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Show Email Host and Port fields only for Custom provider */}
                  {selectedProvider === 'Custom' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Email Host
                        </label>
                        <input
                          type="text"
                          name="email_host"
                          value={emailConfig.email_host}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="imap.yourprovider.com"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Port
                        </label>
                        <input
                          type="number"
                          name="email_port"
                          value={emailConfig.email_port}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        />
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      name="email_username"
                      value={emailConfig.email_username}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Password / App Password
                    </label>
                    <input
                      type="password"
                      name="email_password"
                      value={emailConfig.email_password}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Your email password or app password"
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      For Gmail, use an App Password instead of your regular password
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Folder
                      </label>
                      <input
                        type="text"
                        name="folder"
                        value={emailConfig.folder}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="INBOX"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Max Emails
                      </label>
                      <input
                        type="number"
                        name="max_emails"
                        value={emailConfig.max_emails}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        min="1"
                        max="100"
                      />
                    </div>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="use_ssl"
                      checked={emailConfig.use_ssl}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-700">
                      Use SSL/TLS (recommended)
                    </label>
                  </div>

                  <button
                    onClick={handleEmailProcessing}
                    className="w-full btn-primary"
                  >
                    🚀 Start Processing Emails
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Invoice Records */}
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Recent Invoices</h2>
              <button
                onClick={loadCufeRecords}
                className="btn-secondary flex items-center"
                disabled={loading}
              >
                {loading ? <LoadingSpinner size="sm" className="mr-2" /> : null}
                Refresh
              </button>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <p className="text-red-800">{error}</p>
              </div>
            )}

            {loading ? (
              <div className="flex justify-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            ) : cufeRecords.length === 0 ? (
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  No invoice records have been processed for your account yet.
                </p>
              </div>
            ) : (
              <div className="overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        CUFE
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Issuer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Document
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Products
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {cufeRecords.map((record) => {
                      const productCount = getProductCount(record)
                      const isExpanded = expandedInvoices.has(record.cufe_value)

                      return (
                        <React.Fragment key={record.cufe_value}>
                          {/* Main invoice row */}
                          <tr className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-mono text-gray-900">
                                {record.cufe_value.substring(0, 20)}...
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {record.issuer_name || 'N/A'}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {record.document_number || 'N/A'}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {formatAmount(record.total_amount)}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <button
                                  onClick={() => toggleInvoiceExpansion(record.cufe_value)}
                                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                                  disabled={productCount === 0}
                                >
                                  {productCount > 0 && (
                                    <svg
                                      className={`h-4 w-4 mr-1 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                  )}
                                  <span className={productCount > 0 ? 'text-blue-600' : 'text-gray-500'}>
                                    {formatProductCount(productCount)}
                                  </span>
                                </button>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {formatDate(record.issue_date || record.processed_date)}
                              </div>
                            </td>
                          </tr>

                          {/* Expanded product details */}
                          {isExpanded && record.line_items && record.line_items.length > 0 && (
                            <tr className="bg-blue-50">
                              <td colSpan={6} className="px-6 py-4">
                                <div className="bg-white rounded-lg border border-blue-200 p-4">
                                  <h4 className="text-sm font-medium text-gray-900 mb-3">
                                    Detalle de Productos ({record.line_items.length})
                                  </h4>
                                  <div className="overflow-x-auto">
                                    <table className="min-w-full">
                                      <thead>
                                        <tr className="border-b border-gray-200">
                                          <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Producto</th>
                                          <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Cantidad</th>
                                          <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Unidad</th>
                                          <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Precio Unit.</th>
                                          <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Subtotal</th>
                                          <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">IVA</th>
                                          <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Total</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {record.line_items.map((item, index) => (
                                          <tr key={index} className="border-b border-gray-100">
                                            <td className="py-2 text-sm">
                                              <div className="font-medium text-gray-900">{item.item_name || 'N/A'}</div>
                                              {item.item_description && (
                                                <div className="text-xs text-gray-500 mt-1">{item.item_description}</div>
                                              )}
                                            </td>
                                            <td className="py-2 text-sm text-gray-900">{item.invoiced_quantity || 'N/A'}</td>
                                            <td className="py-2 text-sm text-gray-900">{item.unit_of_measure || 'N/A'}</td>
                                            <td className="py-2 text-sm text-gray-900">{formatAmount(item.unit_price)}</td>
                                            <td className="py-2 text-sm text-gray-900">{formatAmount(item.line_extension_amount)}</td>
                                            <td className="py-2 text-sm text-gray-900">{formatAmount(item.line_tax_amount)}</td>
                                            <td className="py-2 text-sm font-medium text-gray-900">
                                              {formatAmount(item.line_tax_inclusive_amount)}
                                            </td>
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </td>
                            </tr>
                          )}
                        </React.Fragment>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}

export default DashboardPage
