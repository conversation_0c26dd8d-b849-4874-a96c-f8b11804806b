# 🚀 Complete Configuration Guide

## Overview

This guide explains all configuration options for the Colombian Electronic Invoice Processing System, including the new **LLM-based dynamic XML extraction** and **JWT authentication**.

## 📋 Configuration Files

### 1. **Environment Variables (.env)**

Create a `.env` file in the project root with the following configuration:

```bash
# ===== LLM PROVIDER CONFIGURATION =====
# Choose your preferred LLM provider for dynamic XML extraction
LLM_PROVIDER=openai  # Options: "openai" or "groq"

# OpenAI Configuration (Recommended for best accuracy)
OPENAI_API_KEY=your-actual-openai-api-key-here
LLM_MODEL=gpt-4o-mini  # Options: gpt-4o-mini, gpt-4, gpt-3.5-turbo

# Groq Configuration (Free alternative with good performance)
GROQ_API_KEY=your-groq-api-key-here
GROQ_MODEL=llama-3.1-70b-versatile  # Options: llama-3.1-70b-versatile, llama-3.1-8b-instant

# ===== JWT AUTHENTICATION =====
# CRITICAL: Change JWT_SECRET_KEY in production!
JWT_SECRET_KEY=your_super_secret_jwt_key_change_this_in_production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===== DATABASE CONFIGURATION =====
POSTGRES_DB=cufe_db
POSTGRES_USER=cufe_user
POSTGRES_PASSWORD=cufe_password_123
DATABASE_URL=postgresql://cufe_user:cufe_password_123@localhost:5432/cufe_db

# ===== EMAIL CONFIGURATION =====
EMAIL_HOST=imap.gmail.com
EMAIL_PORT=993
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_USE_SSL=true
```

## 🔑 API Keys Setup

### **Option A: OpenAI (Recommended)**

1. **Create Account**: Go to [OpenAI Platform](https://platform.openai.com/)
2. **Generate API Key**: Navigate to [API Keys](https://platform.openai.com/account/api-keys)
3. **Create New Key**: Click "Create new secret key"
4. **Copy Key**: Add to `.env` as `OPENAI_API_KEY=sk-...`

**Cost Estimate:**
- **gpt-4o-mini**: ~$0.01 per invoice extraction
- **gpt-4**: ~$0.03 per invoice extraction
- **Monthly (1000 invoices)**: $10-30 USD

### **Option B: Groq (Free Alternative)**

1. **Create Account**: Go to [Groq Console](https://console.groq.com/)
2. **Generate API Key**: Navigate to [API Keys](https://console.groq.com/keys)
3. **Create New Key**: Click "Create API Key"
4. **Copy Key**: Add to `.env` as `GROQ_API_KEY=gsk_...`

**Free Tier:**
- **14,400 requests/day** free
- **Fast inference** with Llama models
- **Good accuracy** for invoice extraction

## 🔐 JWT Authentication Explained

### **What is JWT?**
JWT (JSON Web Token) is used for **secure API authentication**. It's required for:
- Dashboard login system
- API endpoint protection
- User session management
- Secure invoice data access

### **Configuration Parameters:**

```bash
# Secret key for signing tokens (MUST be changed in production)
JWT_SECRET_KEY=your_super_secret_jwt_key_change_this_in_production

# Algorithm for token signing (HS256 is standard)
JWT_ALGORITHM=HS256

# Token expiration time (30 minutes default)
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### **Generate Secure JWT Secret:**

```bash
# Option 1: Using OpenSSL
openssl rand -hex 32

# Option 2: Using Python
python -c "import secrets; print(secrets.token_hex(32))"

# Option 3: Online generator
# Visit: https://generate-secret.vercel.app/32
```

**⚠️ SECURITY WARNING:**
- **NEVER** use the default JWT secret in production
- **ALWAYS** generate a unique, random secret key
- **KEEP** the secret key confidential and secure

## 🤖 LLM Provider Comparison

| Feature | OpenAI | Groq |
|---------|--------|------|
| **Accuracy** | Excellent (95%+) | Very Good (90%+) |
| **Speed** | Good (2-5s) | Excellent (<1s) |
| **Cost** | Paid ($0.01-0.03/extraction) | Free tier available |
| **Models** | GPT-4, GPT-4o-mini | Llama 3.1, Mixtral |
| **Reliability** | Very High | High |
| **Rate Limits** | Generous (paid) | 14,400/day (free) |

## 🔧 Advanced Configuration

### **LLM Model Selection:**

#### **For High Accuracy (Recommended):**
```bash
LLM_PROVIDER=openai
LLM_MODEL=gpt-4o-mini  # Best balance of cost/accuracy
```

#### **For Maximum Accuracy:**
```bash
LLM_PROVIDER=openai
LLM_MODEL=gpt-4  # Highest accuracy, higher cost
```

#### **For Speed & Free Usage:**
```bash
LLM_PROVIDER=groq
GROQ_MODEL=llama-3.1-70b-versatile  # Best Groq model
```

#### **For Maximum Speed:**
```bash
LLM_PROVIDER=groq
GROQ_MODEL=llama-3.1-8b-instant  # Fastest processing
```

### **Fallback Behavior:**
The system automatically falls back to traditional XPath extraction when:
- LLM API key is invalid or missing
- Rate limits are exceeded
- Network connectivity issues occur
- LLM service is unavailable

This ensures **100% uptime** and reliability.

## 🧪 Testing Configuration

### **Test LLM Setup:**
```bash
python -c "
import sys
sys.path.append('.')
import os

# Set your configuration
os.environ['LLM_PROVIDER'] = 'openai'  # or 'groq'
os.environ['OPENAI_API_KEY'] = 'your-key-here'

from shared.services.llm_extraction_service import LLMExtractionService
llm_service = LLMExtractionService()
print('✅ LLM service configured successfully')
"
```

### **Test Complete Extraction:**
```bash
python -c "
import sys
sys.path.append('.')
from services.extraction_service.main import _extract_cufe_from_xml

result = _extract_cufe_from_xml('factura_prueba_completa.xml', True)
print(f'✅ Extraction successful: {result.cufe_value[:20]}...')
"
```

## 🚨 Troubleshooting

### **Common Issues:**

1. **"Invalid API key"**
   - ✅ Check `.env` file exists
   - ✅ Verify API key is correct
   - ✅ Ensure no extra spaces in key

2. **"Rate limit exceeded"**
   - ✅ Switch to different provider
   - ✅ Upgrade API plan
   - ✅ System will auto-fallback to XPath

3. **"JWT token invalid"**
   - ✅ Check JWT_SECRET_KEY is set
   - ✅ Verify token hasn't expired
   - ✅ Ensure consistent secret across services

4. **"Database connection failed"**
   - ✅ Check PostgreSQL is running
   - ✅ Verify DATABASE_URL is correct
   - ✅ Test database connectivity

## 📊 Monitoring

The system provides detailed logging for all operations:

```
INFO - Attempting LLM-based extraction for: invoice.xml
INFO - LLM extraction successful using openai
WARNING - LLM extraction failed, falling back to XPath method
INFO - Using XPath extraction for: invoice.xml
```

Monitor these logs to track:
- LLM success/failure rates
- API usage patterns
- Performance metrics
- Error frequencies

## 🔄 Production Deployment

### **Pre-deployment Checklist:**

- [ ] Generate secure JWT secret key
- [ ] Configure production database
- [ ] Set up valid LLM API keys
- [ ] Test email configuration
- [ ] Verify all environment variables
- [ ] Test extraction with sample invoices
- [ ] Configure logging and monitoring
- [ ] Set up backup procedures

### **Environment-specific Configuration:**

```bash
# Development
DEBUG=true
LOG_LEVEL=DEBUG

# Production
DEBUG=false
LOG_LEVEL=INFO
```

This configuration ensures your Colombian Electronic Invoice Processing System runs optimally with intelligent LLM-based extraction and robust security! 🚀
