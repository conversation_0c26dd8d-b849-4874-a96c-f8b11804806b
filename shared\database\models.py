"""
SQLAlchemy database models
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Text, Boolean, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .connection import Base

class Client(Base):
    """
    Model for storing client information
    """
    __tablename__ = "clients"

    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(String(100), unique=True, index=True, nullable=False)
    company_name = Column(String(255), nullable=False)
    email_domain = Column(String(255), nullable=True)  # For automatic client assignment
    created_date = Column(DateTime, default=func.now())
    is_active = Column(Boolean, default=True)

    # Relationships
    users = relationship("User", back_populates="client")
    email_records = relationship("EmailRecord", back_populates="client")

class User(Base):
    """
    Model for storing user authentication information
    """
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_date = Column(DateTime, default=func.now())
    last_login = Column(DateTime, nullable=True)

    # Relationships
    client = relationship("Client", back_populates="users")

class EmailRecord(Base):
    """
    Model for storing email processing records
    """
    __tablename__ = "email_records"

    id = Column(Integer, primary_key=True, index=True)
    email_id = Column(String(255), unique=True, index=True, nullable=False)
    sender = Column(String(255), nullable=False)
    subject = Column(Text)
    reception_date = Column(DateTime, nullable=False)
    processed_date = Column(DateTime, default=func.now())
    has_zip_attachment = Column(Boolean, default=False)
    processing_status = Column(String(50), default="pending")  # pending, processing, completed, failed
    error_message = Column(Text, nullable=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)

    # Relationships
    client = relationship("Client", back_populates="email_records")
    cufe_records = relationship("CUFERecord", back_populates="email_record")

class ZipFileRecord(Base):
    """
    Model for storing ZIP file processing records
    """
    __tablename__ = "zip_file_records"
    
    id = Column(Integer, primary_key=True, index=True)
    email_record_id = Column(Integer, ForeignKey("email_records.id"), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)
    download_date = Column(DateTime, default=func.now())
    extraction_status = Column(String(50), default="pending")  # pending, extracted, failed
    extracted_files_count = Column(Integer, default=0)
    
    # Relationship to email record
    email_record = relationship("EmailRecord")
    
    # Relationship to extracted files
    extracted_files = relationship("ExtractedFileRecord", back_populates="zip_file_record")

class ExtractedFileRecord(Base):
    """
    Model for storing extracted file records
    """
    __tablename__ = "extracted_file_records"
    
    id = Column(Integer, primary_key=True, index=True)
    zip_file_record_id = Column(Integer, ForeignKey("zip_file_records.id"), nullable=False)
    filename = Column(String(255), nullable=False)
    file_type = Column(String(10), nullable=False)  # xml, pdf, other
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)
    extraction_date = Column(DateTime, default=func.now())
    
    # Relationship to ZIP file record
    zip_file_record = relationship("ZipFileRecord", back_populates="extracted_files")
    
    # Relationship to CUFE records (for XML files)
    cufe_records_as_xml = relationship("CUFERecord", foreign_keys="CUFERecord.xml_file_record_id", back_populates="xml_file_record")
    cufe_records_as_pdf = relationship("CUFERecord", foreign_keys="CUFERecord.pdf_file_record_id", back_populates="pdf_file_record")

class CUFERecord(Base):
    """
    Model for storing CUFE extraction records
    """
    __tablename__ = "cufe_records"
    
    id = Column(Integer, primary_key=True, index=True)
    cufe_value = Column(String(255), unique=True, index=True, nullable=False)
    email_record_id = Column(Integer, ForeignKey("email_records.id"), nullable=False)
    xml_file_record_id = Column(Integer, ForeignKey("extracted_file_records.id"), nullable=False)
    pdf_file_record_id = Column(Integer, ForeignKey("extracted_file_records.id"), nullable=True)
    extraction_date = Column(DateTime, default=func.now())
    
    # Additional XML data that might be useful
    issuer_name = Column(String(255), nullable=True)
    document_number = Column(String(100), nullable=True)
    issue_date = Column(DateTime, nullable=True)
    total_amount = Column(String(50), nullable=True)

    # Enhanced tax and monetary details
    tax_exclusive_amount = Column(String(50), nullable=True)  # Amount before taxes
    tax_inclusive_amount = Column(String(50), nullable=True)  # Amount including taxes
    allowance_total_amount = Column(String(50), nullable=True)  # Total discounts
    charge_total_amount = Column(String(50), nullable=True)  # Total additional charges
    prepaid_amount = Column(String(50), nullable=True)  # Prepaid amounts
    payable_amount = Column(String(50), nullable=True)  # Final amount to pay

    # Tax breakdown details
    total_tax_amount = Column(String(50), nullable=True)  # Total of all taxes
    iva_amount = Column(String(50), nullable=True)  # IVA (VAT) amount
    rete_fuente_amount = Column(String(50), nullable=True)  # Retención en la fuente
    rete_iva_amount = Column(String(50), nullable=True)  # Retención de IVA
    rete_ica_amount = Column(String(50), nullable=True)  # Retención de ICA

    # Additional invoice details
    due_date = Column(DateTime, nullable=True)  # Payment due date
    currency_code = Column(String(10), nullable=True)  # Currency (usually COP)
    invoice_type_code = Column(String(10), nullable=True)  # Type of invoice
    accounting_cost = Column(String(100), nullable=True)  # Cost center or accounting reference
    
    # Relationships
    email_record = relationship("EmailRecord", back_populates="cufe_records")
    xml_file_record = relationship("ExtractedFileRecord", foreign_keys=[xml_file_record_id], back_populates="cufe_records_as_xml")
    pdf_file_record = relationship("ExtractedFileRecord", foreign_keys=[pdf_file_record_id], back_populates="cufe_records_as_pdf")
    invoice_line_items = relationship("InvoiceLineItem", back_populates="cufe_record", cascade="all, delete-orphan")
    allowance_charges = relationship("InvoiceAllowanceCharge", back_populates="cufe_record", cascade="all, delete-orphan")
    payment_terms = relationship("InvoicePaymentTerm", back_populates="cufe_record", cascade="all, delete-orphan")

class InvoiceLineItem(Base):
    """
    Model for storing individual invoice line items extracted from UBL XML
    """
    __tablename__ = "invoice_line_items"

    id = Column(Integer, primary_key=True, index=True)
    cufe_record_id = Column(Integer, ForeignKey("cufe_records.id"), nullable=False)
    line_number = Column(Integer, nullable=True)  # Line sequence number from XML

    # Item identification
    item_name = Column(String(500), nullable=True)
    item_description = Column(Text, nullable=True)
    item_code = Column(String(100), nullable=True)  # Standard item code if available

    # Quantities and units
    invoiced_quantity = Column(String(50), nullable=True)  # Store as string to preserve precision
    unit_of_measure = Column(String(50), nullable=True)

    # Pricing information
    unit_price = Column(String(50), nullable=True)  # Unit price before taxes
    line_extension_amount = Column(String(50), nullable=True)  # Line total before taxes

    # Tax information per line
    line_tax_amount = Column(String(50), nullable=True)  # Total tax for this line
    line_tax_inclusive_amount = Column(String(50), nullable=True)  # Line total including taxes

    # Additional line details
    allowance_charge_amount = Column(String(50), nullable=True)  # Line-level discounts/charges
    free_of_charge_indicator = Column(Boolean, default=False)  # If item is free

    # Relationship
    cufe_record = relationship("CUFERecord", back_populates="invoice_line_items")

class InvoiceAllowanceCharge(Base):
    """
    Model for storing invoice-level allowances (discounts) and charges
    """
    __tablename__ = "invoice_allowance_charges"

    id = Column(Integer, primary_key=True, index=True)
    cufe_record_id = Column(Integer, ForeignKey("cufe_records.id"), nullable=False)

    # Allowance or Charge indicator
    charge_indicator = Column(Boolean, nullable=False)  # True for charge, False for allowance

    # Allowance/Charge details
    allowance_charge_reason_code = Column(String(10), nullable=True)  # Reason code
    allowance_charge_reason = Column(String(255), nullable=True)  # Reason description
    multiplier_factor_numeric = Column(String(20), nullable=True)  # Percentage if applicable
    amount = Column(String(50), nullable=True)  # Amount of allowance/charge
    base_amount = Column(String(50), nullable=True)  # Base amount for percentage calculation

    # Tax information for the allowance/charge
    tax_category = Column(String(50), nullable=True)
    tax_amount = Column(String(50), nullable=True)

    # Relationship
    cufe_record = relationship("CUFERecord", back_populates="allowance_charges")

class InvoicePaymentTerm(Base):
    """
    Model for storing payment terms and conditions
    """
    __tablename__ = "invoice_payment_terms"

    id = Column(Integer, primary_key=True, index=True)
    cufe_record_id = Column(Integer, ForeignKey("cufe_records.id"), nullable=False)

    # Payment terms details
    payment_means_code = Column(String(10), nullable=True)  # Payment method code
    payment_due_date = Column(DateTime, nullable=True)  # When payment is due
    payment_terms_note = Column(Text, nullable=True)  # Additional payment notes

    # Settlement period
    settlement_period_measure = Column(String(20), nullable=True)  # Number of days/months
    settlement_period_unit = Column(String(10), nullable=True)  # DAY, MONTH, etc.

    # Payment amounts
    settlement_discount_percent = Column(String(10), nullable=True)  # Early payment discount
    penalty_surcharge_percent = Column(String(10), nullable=True)  # Late payment penalty
    amount = Column(String(50), nullable=True)  # Payment amount if specific

    # Relationship
    cufe_record = relationship("CUFERecord", back_populates="payment_terms")

class ProcessingLog(Base):
    """
    Model for storing processing logs and audit trail
    """
    __tablename__ = "processing_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    service_name = Column(String(100), nullable=False)
    operation = Column(String(100), nullable=False)
    status = Column(String(50), nullable=False)  # success, error, warning
    message = Column(Text)
    details = Column(Text, nullable=True)  # JSON string for additional details
    timestamp = Column(DateTime, default=func.now())
    
    # Optional reference to related records
    email_record_id = Column(Integer, ForeignKey("email_records.id"), nullable=True)
    cufe_record_id = Column(Integer, ForeignKey("cufe_records.id"), nullable=True)
