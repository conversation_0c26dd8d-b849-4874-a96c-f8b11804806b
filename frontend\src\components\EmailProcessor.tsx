import React, { useState } from 'react'
import { fileProcessingApi, ProcessEmailsRequest } from '../services/api'
import LoadingSpinner from './LoadingSpinner'

interface EmailProcessorProps {
  onProcessSuccess?: (result: any) => void
  onProcessError?: (error: string) => void
}

const EmailProcessor: React.FC<EmailProcessorProps> = ({ onProcessSuccess, onProcessError }) => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [selectedProvider, setSelectedProvider] = useState('Gmail')
  const [formData, setFormData] = useState<ProcessEmailsRequest>({
    email_host: 'imap.gmail.com',
    email_port: 993,
    email_username: '',
    email_password: '',
    use_ssl: true,
    folder: 'INBOX',
    date_filter: '',
    max_emails: 10
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
              type === 'number' ? parseInt(value) || 0 : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.email_username || !formData.email_password) {
      onProcessError?.('Please provide email credentials')
      return
    }

    try {
      setIsProcessing(true)
      const result = await fileProcessingApi.processEmails(formData)
      onProcessSuccess?.(result)
    } catch (error: any) {
      console.error('Email processing error:', error)
      onProcessError?.(error.response?.data?.detail || 'Failed to process emails')
    } finally {
      setIsProcessing(false)
    }
  }

  const emailProviders = [
    { name: 'Gmail', host: 'imap.gmail.com', port: 993, ssl: true },
    { name: 'Outlook', host: 'outlook.office365.com', port: 993, ssl: true },
    { name: 'Yahoo', host: 'imap.mail.yahoo.com', port: 993, ssl: true },
    { name: 'Custom', host: '', port: 993, ssl: true }
  ]

  const handleProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const providerName = e.target.value
    setSelectedProvider(providerName)

    const provider = emailProviders.find(p => p.name === providerName)
    if (provider && provider.host) {
      setFormData(prev => ({
        ...prev,
        email_host: provider.host,
        email_port: provider.port,
        use_ssl: provider.ssl
      }))
    }
  }

  return (
    <div className="w-full max-w-2xl">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email Provider Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Provider
          </label>
          <select
            value={selectedProvider}
            onChange={handleProviderChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {emailProviders.map(provider => (
              <option key={provider.name} value={provider.name}>
                {provider.name}
              </option>
            ))}
          </select>
        </div>

        {/* Show Email Host and Port fields only for Custom provider */}
        {selectedProvider === 'Custom' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Host
              </label>
              <input
                type="text"
                name="email_host"
                value={formData.email_host}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="imap.yourprovider.com"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Port
              </label>
              <input
                type="number"
                name="email_port"
                value={formData.email_port}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
          </div>
        )}

        {/* Credentials */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <input
            type="email"
            name="email_username"
            value={formData.email_username}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Password / App Password
          </label>
          <input
            type="password"
            name="email_password"
            value={formData.email_password}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Your email password or app password"
            required
          />
          <p className="mt-1 text-xs text-gray-500">
            For Gmail, use an App Password instead of your regular password
          </p>
        </div>

        {/* Advanced Options */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Folder
            </label>
            <input
              type="text"
              name="folder"
              value={formData.folder}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="INBOX"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Filter (optional)
            </label>
            <input
              type="date"
              name="date_filter"
              value={formData.date_filter}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Emails
            </label>
            <input
              type="number"
              name="max_emails"
              value={formData.max_emails}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              min="1"
              max="100"
            />
          </div>
        </div>

        {/* SSL Option */}
        <div className="flex items-center">
          <input
            type="checkbox"
            name="use_ssl"
            checked={formData.use_ssl}
            onChange={handleInputChange}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label className="ml-2 block text-sm text-gray-700">
            Use SSL/TLS (recommended)
          </label>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isProcessing}
          className="w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Processing Emails...
            </>
          ) : (
            '🚀 Process Emails'
          )}
        </button>
      </form>
    </div>
  )
}

export default EmailProcessor
