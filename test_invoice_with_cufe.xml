<?xml version="1.0" encoding="UTF-8"?>
<fe:Invoice xmlns:fe="http://www.dian.gov.co/contratos/facturaelectronica/v1"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
  
  <!-- CUFE - This is what the system needs to find -->
  <cbc:UUID schemeName="CUFE">a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456</cbc:UUID>
  
  <!-- Basic Invoice Information -->
  <cbc:UBLVersionID>2.1</cbc:UBLVersionID>
  <cbc:CustomizationID>DIAN 2.1</cbc:CustomizationID>
  <cbc:ID>FVE-2025-001</cbc:ID>
  <cbc:IssueDate>2025-09-12</cbc:IssueDate>
  <cbc:IssueTime>10:30:00</cbc:IssueTime>
  <cbc:InvoiceTypeCode>01</cbc:InvoiceTypeCode>
  <cbc:DocumentCurrencyCode>COP</cbc:DocumentCurrencyCode>

  <!-- Supplier Information -->
  <cac:AccountingSupplierParty>
    <cac:Party>
      <cac:PartyIdentification>
        <cbc:ID schemeID="31">*********</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>Empresa de Prueba S.A.S.</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Calle 123 #45-67</cbc:StreetName>
        <cbc:CityName>Bogotá</cbc:CityName>
        <cbc:PostalZone>110111</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>CO</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:RegistrationName>Empresa de Prueba S.A.S.</cbc:RegistrationName>
        <cbc:CompanyID schemeID="31">*********</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>01</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
    </cac:Party>
  </cac:AccountingSupplierParty>

  <!-- Customer Information -->
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cac:PartyIdentification>
        <cbc:ID schemeID="31">*********</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>Cliente Ejemplo Ltda.</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Carrera 98 #76-54</cbc:StreetName>
        <cbc:CityName>Medellín</cbc:CityName>
        <cbc:PostalZone>050001</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>CO</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
    </cac:Party>
  </cac:AccountingCustomerParty>

  <!-- Invoice Lines -->
  <cac:InvoiceLine>
    <cbc:ID>1</cbc:ID>
    <cbc:InvoicedQuantity unitCode="EA">2</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="COP">200000.00</cbc:LineExtensionAmount>
    <cac:Item>
      <cbc:Name>Servicio de Consultoría Técnica</cbc:Name>
      <cbc:Description>Consultoría especializada en desarrollo de software</cbc:Description>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="COP">100000.00</cbc:PriceAmount>
    </cac:Price>
    <cac:TaxTotal>
      <cbc:TaxAmount currencyID="COP">38000.00</cbc:TaxAmount>
      <cac:TaxSubtotal>
        <cbc:TaxableAmount currencyID="COP">200000.00</cbc:TaxableAmount>
        <cbc:TaxAmount currencyID="COP">38000.00</cbc:TaxAmount>
        <cac:TaxCategory>
          <cbc:ID>01</cbc:ID>
          <cbc:Percent>19.00</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>01</cbc:ID>
          </cac:TaxScheme>
        </cac:TaxCategory>
      </cac:TaxSubtotal>
    </cac:TaxTotal>
  </cac:InvoiceLine>

  <cac:InvoiceLine>
    <cbc:ID>2</cbc:ID>
    <cbc:InvoicedQuantity unitCode="KG">5</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="COP">150000.00</cbc:LineExtensionAmount>
    <cac:Item>
      <cbc:Name>Material de Oficina</cbc:Name>
      <cbc:Description>Papel bond tamaño carta</cbc:Description>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="COP">30000.00</cbc:PriceAmount>
    </cac:Price>
    <cac:TaxTotal>
      <cbc:TaxAmount currencyID="COP">28500.00</cbc:TaxAmount>
      <cac:TaxSubtotal>
        <cbc:TaxableAmount currencyID="COP">150000.00</cbc:TaxableAmount>
        <cbc:TaxAmount currencyID="COP">28500.00</cbc:TaxAmount>
        <cac:TaxCategory>
          <cbc:ID>01</cbc:ID>
          <cbc:Percent>19.00</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>01</cbc:ID>
          </cac:TaxScheme>
        </cac:TaxCategory>
      </cac:TaxSubtotal>
    </cac:TaxTotal>
  </cac:InvoiceLine>

  <!-- Tax Totals -->
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="COP">66500.00</cbc:TaxAmount>
    <cac:TaxSubtotal>
      <cbc:TaxableAmount currencyID="COP">350000.00</cbc:TaxableAmount>
      <cbc:TaxAmount currencyID="COP">66500.00</cbc:TaxAmount>
      <cac:TaxCategory>
        <cbc:ID>01</cbc:ID>
        <cbc:Percent>19.00</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>01</cbc:ID>
        </cac:TaxScheme>
      </cac:TaxCategory>
    </cac:TaxSubtotal>
  </cac:TaxTotal>

  <!-- Monetary Totals -->
  <cac:LegalMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="COP">350000.00</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="COP">350000.00</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="COP">416500.00</cbc:TaxInclusiveAmount>
    <cbc:PayableAmount currencyID="COP">416500.00</cbc:PayableAmount>
  </cac:LegalMonetaryTotal>

</fe:Invoice>
