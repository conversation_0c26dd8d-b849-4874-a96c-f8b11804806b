"""
Email connection and processing service
"""

import imaplib
import email
import os
import uuid
from datetime import datetime
from typing import List, Tu<PERSON>, Optional, Dict, Any
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import ssl

from sqlalchemy.orm import Session
from shared.database.models import EmailRecord, ZipFileRecord
from shared.schemas.mailbox import EmailProcessRequest, DownloadedFile, EmailInfo
from shared.utils.logger import get_logger
from shared.utils.file_utils import ensure_directory, get_file_size, safe_filename

logger = get_logger(__name__)

class EmailService:
    """
    Service for connecting to email servers and processing emails
    """
    
    def __init__(self, temp_files_path: str = "./temp_files"):
        self.temp_files_path = temp_files_path
        ensure_directory(temp_files_path)
    
    def connect_to_mailbox(self, request: EmailProcessRequest) -> imaplib.IMAP4_SSL:
        """
        Connect to email server using IMAP
        
        Args:
            request: Email connection parameters
            
        Returns:
            IMAP connection object
            
        Raises:
            Exception: If connection fails
        """
        try:
            logger.info(f"Connecting to {request.email_host}:{request.email_port}")
            
            if request.use_ssl:
                # Create SSL context
                context = ssl.create_default_context()
                mail = imaplib.IMAP4_SSL(request.email_host, request.email_port, ssl_context=context)
            else:
                mail = imaplib.IMAP4(request.email_host, request.email_port)
            
            # Login
            mail.login(request.email_username, request.email_password)
            logger.info("Successfully connected to mailbox")
            
            return mail
            
        except Exception as e:
            logger.error(f"Failed to connect to mailbox: {str(e)}")
            raise Exception(f"Email connection failed: {str(e)}")
    
    def search_emails_with_attachments(
        self, 
        mail: imaplib.IMAP4_SSL, 
        request: EmailProcessRequest
    ) -> List[bytes]:
        """
        Search for emails with attachments
        
        Args:
            mail: IMAP connection
            request: Email processing request
            
        Returns:
            List of email IDs
        """
        try:
            # Select folder
            mail.select(request.folder)
            
            # Build search criteria
            search_criteria = ['ALL']
            
            if request.date_filter:
                search_criteria.append(request.date_filter)
            
            # Search for emails
            status, messages = mail.search(None, *search_criteria)
            
            if status != 'OK':
                raise Exception("Failed to search emails")
            
            email_ids = messages[0].split()
            
            # Limit results if specified
            if request.max_emails and len(email_ids) > request.max_emails:
                email_ids = email_ids[-request.max_emails:]  # Get most recent emails
            
            logger.info(f"Found {len(email_ids)} emails to process")
            return email_ids
            
        except Exception as e:
            logger.error(f"Failed to search emails: {str(e)}")
            raise
    
    def get_email_info(self, mail: imaplib.IMAP4_SSL, email_id: bytes) -> Dict[str, Any]:
        """
        Get email information and check for ZIP attachments
        
        Args:
            mail: IMAP connection
            email_id: Email ID
            
        Returns:
            Dictionary with email information
        """
        try:
            # Fetch email
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            
            if status != 'OK':
                raise Exception(f"Failed to fetch email {email_id}")
            
            # Parse email
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # Extract basic info
            subject = email_message.get('Subject', '')
            sender = email_message.get('From', '')
            date_str = email_message.get('Date', '')
            
            # Parse date
            reception_date = datetime.now()
            if date_str:
                try:
                    reception_date = email.utils.parsedate_to_datetime(date_str)
                except:
                    pass
            
            # Check for ZIP attachments
            zip_attachments = []
            attachment_count = 0
            
            for part in email_message.walk():
                if part.get_content_disposition() == 'attachment':
                    attachment_count += 1
                    filename = part.get_filename()
                    if filename and filename.lower().endswith('.zip'):
                        zip_attachments.append({
                            'filename': filename,
                            'part': part
                        })
            
            return {
                'email_id': email_id.decode(),
                'subject': subject,
                'sender': sender,
                'reception_date': reception_date,
                'has_zip_attachment': len(zip_attachments) > 0,
                'attachment_count': attachment_count,
                'zip_attachments': zip_attachments,
                'email_message': email_message
            }
            
        except Exception as e:
            logger.error(f"Failed to get email info for {email_id}: {str(e)}")
            raise
    
    def download_zip_attachments(
        self, 
        email_info: Dict[str, Any], 
        db: Session
    ) -> List[DownloadedFile]:
        """
        Download ZIP attachments from email
        
        Args:
            email_info: Email information dictionary
            db: Database session
            
        Returns:
            List of downloaded files
        """
        downloaded_files = []
        
        try:
            for attachment in email_info['zip_attachments']:
                filename = safe_filename(attachment['filename'])
                
                # Generate unique filename
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(self.temp_files_path, unique_filename)
                
                # Download attachment
                with open(file_path, 'wb') as f:
                    f.write(attachment['part'].get_payload(decode=True))
                
                file_size = get_file_size(file_path)
                download_date = datetime.now()
                
                # Create downloaded file info
                downloaded_file = DownloadedFile(
                    filename=attachment['filename'],
                    file_path=file_path,
                    file_size=file_size,
                    email_id=email_info['email_id'],
                    download_date=download_date
                )
                
                downloaded_files.append(downloaded_file)
                
                logger.info(f"Downloaded ZIP attachment: {filename} ({file_size} bytes)")
            
            return downloaded_files
            
        except Exception as e:
            logger.error(f"Failed to download ZIP attachments: {str(e)}")
            raise
    
    def store_email_record(self, email_info: Dict[str, Any], db: Session, client_id: Optional[int] = None) -> EmailRecord:
        """
        Store email record in database

        Args:
            email_info: Email information
            db: Database session
            client_id: Client ID to associate with the email record

        Returns:
            Created EmailRecord
        """
        try:
            # Validate client_id is provided
            if client_id is None:
                logger.warning(f"No client_id provided for email {email_info['email_id']}, email will not be associated with any client")

            # Check if email already exists
            existing_email = db.query(EmailRecord).filter(
                EmailRecord.email_id == email_info['email_id']
            ).first()

            if existing_email:
                logger.info(f"Email {email_info['email_id']} already exists in database")

                # Update client_id if it's missing or different
                if existing_email.client_id != client_id and client_id is not None:
                    logger.info(f"Updating client_id for existing email {email_info['email_id']} from {existing_email.client_id} to {client_id}")
                    existing_email.client_id = client_id
                    db.commit()
                    db.refresh(existing_email)

                return existing_email

            # Create new email record
            email_record = EmailRecord(
                email_id=email_info['email_id'],
                sender=email_info['sender'],
                subject=email_info['subject'],
                reception_date=email_info['reception_date'],
                has_zip_attachment=email_info['has_zip_attachment'],
                processing_status="processing",
                client_id=client_id
            )
            
            db.add(email_record)
            db.commit()
            db.refresh(email_record)

            logger.info(f"Stored email record: {email_info['email_id']} with client_id: {client_id}")
            return email_record
            
        except Exception as e:
            logger.error(f"Failed to store email record: {str(e)}")
            db.rollback()
            raise
    
    def store_zip_file_records(
        self, 
        downloaded_files: List[DownloadedFile], 
        email_record: EmailRecord, 
        db: Session
    ) -> List[ZipFileRecord]:
        """
        Store ZIP file records in database
        
        Args:
            downloaded_files: List of downloaded files
            email_record: Associated email record
            db: Database session
            
        Returns:
            List of created ZipFileRecord objects
        """
        zip_records = []
        
        try:
            for downloaded_file in downloaded_files:
                zip_record = ZipFileRecord(
                    email_record_id=email_record.id,
                    original_filename=downloaded_file.filename,
                    file_path=downloaded_file.file_path,
                    file_size=downloaded_file.file_size,
                    download_date=downloaded_file.download_date,
                    extraction_status="pending"
                )
                
                db.add(zip_record)
                zip_records.append(zip_record)
            
            db.commit()
            
            for record in zip_records:
                db.refresh(record)
            
            logger.info(f"Stored {len(zip_records)} ZIP file records")
            return zip_records
            
        except Exception as e:
            logger.error(f"Failed to store ZIP file records: {str(e)}")
            db.rollback()
            raise
    
    def process_emails(self, request: EmailProcessRequest, db: Session) -> Tuple[int, List[DownloadedFile], List[str]]:
        """
        Main method to process emails
        
        Args:
            request: Email processing request
            db: Database session
            
        Returns:
            Tuple of (processed_count, downloaded_files, errors)
        """
        mail = None
        processed_count = 0
        downloaded_files = []
        errors = []
        
        try:
            # Connect to mailbox
            mail = self.connect_to_mailbox(request)
            
            # Search for emails
            email_ids = self.search_emails_with_attachments(mail, request)
            
            for email_id in email_ids:
                try:
                    # Get email information
                    email_info = self.get_email_info(mail, email_id)
                    
                    # Store email record
                    email_record = self.store_email_record(email_info, db, request.client_id)
                    
                    # Download ZIP attachments if any
                    if email_info['has_zip_attachment']:
                        email_downloaded_files = self.download_zip_attachments(email_info, db)
                        downloaded_files.extend(email_downloaded_files)
                        
                        # Store ZIP file records
                        self.store_zip_file_records(email_downloaded_files, email_record, db)
                    
                    # Update email record status
                    email_record.processing_status = "completed"
                    db.commit()
                    
                    processed_count += 1
                    
                except Exception as e:
                    error_msg = f"Failed to process email {email_id}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    
                    # Update email record with error
                    try:
                        email_record = db.query(EmailRecord).filter(
                            EmailRecord.email_id == email_id.decode()
                        ).first()
                        if email_record:
                            email_record.processing_status = "failed"
                            email_record.error_message = str(e)
                            db.commit()
                    except:
                        pass
            
            return processed_count, downloaded_files, errors
            
        except Exception as e:
            error_msg = f"Email processing failed: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            return processed_count, downloaded_files, errors
            
        finally:
            if mail:
                try:
                    mail.close()
                    mail.logout()
                except:
                    pass
