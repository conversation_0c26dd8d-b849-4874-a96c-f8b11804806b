# LLM-Based Dynamic XML Extraction

## Overview

The system now supports **dynamic XML extraction using Large Language Models (LLM)** for Colombian electronic invoices. This provides more flexible and intelligent data extraction compared to traditional XPath methods.

## Features

### ✅ **Dynamic Extraction**
- **Intelligent parsing** of XML content using GPT-4
- **Automatic field mapping** to structured data schemas
- **Context-aware extraction** that understands invoice semantics
- **Multi-format support** for different XML structures

### ✅ **Robust Fallback System**
- **Primary method**: LLM-based extraction
- **Fallback method**: Traditional XPath extraction
- **Automatic switching** when LLM fails or is unavailable
- **Zero downtime** - system always works

### ✅ **Enhanced Data Extraction**
- **Complete company information** (issuer and customer)
- **Detailed line items** with all product information
- **Accurate tax calculations** (IVA, retentions)
- **Measurement units** properly mapped to enums
- **Address information** fully structured

## Setup Instructions

### 1. **LLM Provider Configuration**

Create a `.env` file in the project root with your preferred LLM provider:

```bash
# LLM Provider Configuration (choose one)
LLM_PROVIDER=openai  # or "groq"

# OpenAI Configuration
OPENAI_API_KEY=your-actual-openai-api-key-here
LLM_MODEL=gpt-4o-mini  # or gpt-4 for better accuracy

# Groq Configuration (alternative to OpenAI)
GROQ_API_KEY=your-groq-api-key-here
GROQ_MODEL=llama-3.1-70b-versatile  # or other Groq models

# JWT Authentication (required for API security)
JWT_SECRET_KEY=your_super_secret_jwt_key_here_change_this_in_production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 2. **Get API Keys**

#### **Option A: OpenAI (Recommended)**
1. Go to [OpenAI Platform](https://platform.openai.com/account/api-keys)
2. Create a new API key
3. Copy the key to your `.env` file as `OPENAI_API_KEY`

#### **Option B: Groq (Free Alternative)**
1. Go to [Groq Console](https://console.groq.com/keys)
2. Create a new API key
3. Copy the key to your `.env` file as `GROQ_API_KEY`

### 3. **Model Selection**

#### **OpenAI Models:**
- `gpt-4o-mini` - **Cost-effective**, good accuracy (~$0.01/extraction)
- `gpt-4` - **Highest accuracy**, higher cost (~$0.03/extraction)
- `gpt-3.5-turbo` - **Fastest**, lower accuracy (~$0.005/extraction)

#### **Groq Models (Free Tier Available):**
- `llama-3.1-70b-versatile` - **Best balance**, free tier
- `llama-3.1-8b-instant` - **Fastest**, free tier
- `mixtral-8x7b-32768` - **Good accuracy**, free tier

### 4. **JWT Configuration Explained**

The JWT (JSON Web Token) configuration is used for **API authentication and security**:

- **`JWT_SECRET_KEY`**: Secret key used to sign and verify JWT tokens
  - **IMPORTANT**: Change this to a strong, unique secret in production
  - Generate with: `openssl rand -hex 32`

- **`JWT_ALGORITHM`**: Algorithm used for token signing (HS256 is standard)

- **`JWT_ACCESS_TOKEN_EXPIRE_MINUTES`**: How long tokens remain valid (30 minutes default)

**Why JWT is needed:**
- Secures API endpoints from unauthorized access
- Enables user authentication and session management
- Protects sensitive invoice data
- Required for the dashboard login system

## How It Works

### **Extraction Flow:**

```
1. XML File Input
   ↓
2. LLM Extraction Attempt
   ├─ Success → Return structured data
   └─ Failure → Fallback to XPath
      ↓
3. XPath Extraction (Traditional)
   ↓
4. Return extracted data
```

### **LLM Prompt Engineering:**

The system uses a carefully crafted prompt that:
- **Understands Colombian invoice standards**
- **Maps to our exact data schemas**
- **Handles multiple XML formats**
- **Extracts comprehensive information**

## Benefits Over XPath

| Feature | XPath Method | LLM Method |
|---------|-------------|------------|
| **Flexibility** | Fixed patterns only | Adapts to any format |
| **Context Understanding** | None | Full semantic understanding |
| **Error Handling** | Brittle | Robust |
| **New Formats** | Requires code changes | Works automatically |
| **Data Completeness** | Limited | Comprehensive |
| **Maintenance** | High | Low |

## Usage Examples

### **Automatic Usage (Recommended)**
```python
# The system automatically uses LLM with XPath fallback
from services.extraction_service.main import _extract_cufe_from_xml

result = _extract_cufe_from_xml('invoice.xml', extract_additional_data=True)
# Will try LLM first, fallback to XPath if needed
```

### **Direct LLM Usage**
```python
from shared.services.llm_extraction_service import LLMExtractionService

llm_service = LLMExtractionService()
with open('invoice.xml', 'r') as f:
    xml_content = f.read()

result = llm_service.extract_invoice_data(xml_content)
```

## Cost Considerations

### **Token Usage:**
- **Average XML**: ~2,000-4,000 tokens
- **Cost per extraction**: ~$0.01-0.03 USD
- **Monthly volume (1000 invoices)**: ~$10-30 USD

### **Cost Optimization:**
- Use `gpt-4o-mini` for cost efficiency
- Enable XPath fallback to reduce API calls
- Cache results for repeated extractions

## Monitoring and Logging

The system provides detailed logging:

```
INFO - Attempting LLM-based extraction for: invoice.xml
INFO - LLM extraction successful for CUFE: f1e2d3c4b5a6978...
WARNING - LLM extraction failed, falling back to XPath method
INFO - Using XPath extraction for: invoice.xml
```

## Testing

### **Test with Sample Invoice:**
```bash
python -c "
import sys
sys.path.append('.')
from services.extraction_service.main import _extract_cufe_from_xml

result = _extract_cufe_from_xml('factura_prueba_completa.xml', True)
print(f'CUFE: {result.cufe_value}')
print(f'Method: LLM or XPath fallback')
"
```

## Troubleshooting

### **Common Issues:**

1. **"Invalid API key"**
   - Check your `.env` file
   - Verify API key is correct
   - System will fallback to XPath

2. **"Rate limit exceeded"**
   - Reduce extraction frequency
   - Upgrade OpenAI plan
   - System will fallback to XPath

3. **"Model not found"**
   - Check `OPENAI_MODEL` in `.env`
   - Use supported models only

### **Fallback Behavior:**
- **LLM fails** → Automatic XPath fallback
- **No API key** → Direct XPath usage
- **Network issues** → XPath fallback
- **Rate limits** → XPath fallback

## Future Enhancements

- **Local LLM support** (Ollama, etc.)
- **Batch processing** optimization
- **Custom model fine-tuning**
- **Multi-language support**
- **Real-time learning** from corrections
