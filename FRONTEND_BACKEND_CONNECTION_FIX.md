# Frontend-Backend Connection Fix

## Problem Identified
The frontend was not connecting to the backend due to missing environment variable configuration.

## Solution Implemented

### 1. Created Frontend Environment Files

**frontend/.env** (for production/Docker):
```
# API Configuration for containerized environment
# The nginx proxy will forward /api requests to the backend service
VITE_API_URL=/api

# Environment
VITE_NODE_ENV=production
```

**frontend/.env.development** (for local development):
```
# API Configuration for local development
# Points directly to the backend service running on localhost
VITE_API_URL=http://localhost:8000

# Environment
VITE_NODE_ENV=development
```

### 2. Updated Frontend Dockerfile
Added comments to clarify that environment files are copied during build process.

### 3. Configuration Verification

**Frontend API Configuration** (frontend/src/services/api.ts):
- Uses `import.meta.env.VITE_API_URL || '/api'`
- Now properly configured with environment variables

**Nginx Proxy Configuration** (frontend/nginx.conf):
- Forwards `/api/` requests to `http://api-service:8000/`
- Properly aligned with frontend API base URL

**Docker Compose Network**:
- All services on `cufe_network` bridge network
- API service accessible as `api-service:8000` from other containers

## Testing Instructions

### Production Environment (Docker)

#### 1. Build and Start Services
```bash
docker-compose up --build
```

#### 2. Verify Services are Running
```bash
docker-compose ps
```
Expected output should show all services as "Up":
- cufe_postgres
- cufe_api_service
- cufe_frontend
- cufe_mailbox_service
- cufe_file_processing_service
- cufe_extraction_service

#### 3. Test Frontend-Backend Connection
- Access frontend: http://localhost:3000
- Check browser network tab for API calls to `/api/*`
- Verify API calls are successful (not 404 or connection errors)

#### 4. Test API Endpoints Directly
```bash
# Test API health endpoint directly
curl http://localhost:8000/health

# Test through nginx proxy (this should work now)
curl http://localhost:3000/api/health
```

#### 5. Test Login Functionality
- Go to http://localhost:3000
- Try to login (should redirect to login page)
- Check network tab to see if `/api/auth/login` calls are working

### Development Environment (Local)

#### 1. Start Backend Services
```bash
# Start database
docker-compose up postgres -d

# Start API service locally
cd services/api_service
uvicorn main:app --reload --port 8000
```

#### 2. Start Frontend Development Server
```bash
cd frontend
npm run dev
```

#### 3. Test Development Configuration
- Access frontend: http://localhost:3000 (Vite dev server)
- API calls should go directly to http://localhost:8000
- Check network tab to see calls to `http://localhost:8000/*` (not `/api/*`)

#### 4. Verify Environment Variables
```bash
# In frontend directory, check if environment variables are loaded
cd frontend
npm run build
# Check the built files contain the correct API URL
```

## Expected Results

### Production Environment
- Frontend should successfully connect to backend through nginx proxy
- API calls from frontend should go to `/api/*` and be forwarded to backend
- No more "frontend not connected to backend" errors
- Login and other API functionality should work
- Network tab should show successful API calls to `/api/*` endpoints

### Development Environment
- Frontend should connect directly to backend on localhost:8000
- API calls should go directly to `http://localhost:8000/*`
- Hot reload should work for both frontend and backend changes
- Network tab should show successful API calls to `http://localhost:8000/*`

## Troubleshooting

### If Frontend Still Can't Connect to Backend

1. **Check Docker Services Status**
   ```bash
   docker-compose logs api-service
   docker-compose logs frontend
   ```

2. **Verify Environment Variables**
   ```bash
   # Check if .env files exist
   ls -la frontend/.env*

   # Rebuild frontend with new environment variables
   docker-compose build frontend
   ```

3. **Test API Service Directly**
   ```bash
   # Should return {"status": "healthy", "service": "api-service"}
   curl http://localhost:8000/health
   ```

4. **Test Nginx Proxy**
   ```bash
   # Should return the same as above
   curl http://localhost:3000/api/health
   ```

5. **Check Browser Console**
   - Open browser developer tools
   - Look for CORS errors or network failures
   - Verify API calls are going to correct URLs

## Development vs Production Summary
- **Development**: Uses `VITE_API_URL=http://localhost:8000` for direct connection
- **Production/Docker**: Uses `VITE_API_URL=/api` with nginx proxy forwarding to `api-service:8000`
