# Core dependencies for all services
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic[email]==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.13.1

# Authentication
pyjwt==2.8.0
python-multipart==0.0.6
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Email processing
# Note: Using built-in imaplib and email modules

# File processing
lxml==4.9.3

# Excel export
openpyxl==3.1.2

# HTTP client for inter-service communication
httpx==0.25.2

# Utilities
python-dotenv==1.0.0
python-dateutil==2.8.2

# Logging and monitoring
structlog==23.2.0
