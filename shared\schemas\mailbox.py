"""
Pydantic schemas for mailbox service
"""

from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime

class EmailProcessRequest(BaseModel):
    """
    Request schema for email processing
    """
    email_host: str
    email_port: int = 993
    email_username: EmailStr
    email_password: str
    use_ssl: bool = True
    folder: str = "INBOX"
    date_filter: Optional[str] = None  # Format: "SINCE 01-Jan-2024"
    max_emails: Optional[int] = 100
    client_id: Optional[int] = None  # Client ID for associating processed emails

class DownloadedFile(BaseModel):
    """
    Schema for downloaded file information
    """
    filename: str
    file_path: str
    file_size: int
    email_id: str
    download_date: datetime

class EmailProcessResponse(BaseModel):
    """
    Response schema for email processing
    """
    success: bool
    message: str
    processed_count: int
    downloaded_files: List[DownloadedFile]
    errors: Optional[List[str]] = None

class EmailInfo(BaseModel):
    """
    Schema for email information
    """
    email_id: str
    sender: str
    subject: str
    reception_date: datetime
    has_zip_attachment: bool
    attachment_count: int

class EmailListResponse(BaseModel):
    """
    Response schema for listing emails
    """
    emails: List[EmailInfo]
    total_count: int
    has_more: bool
