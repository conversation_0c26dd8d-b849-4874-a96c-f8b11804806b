"""
LLM-based dynamic XML extraction service for Colombian electronic invoices
Uses LangChain for multi-provider support (OpenAI, Groq, etc.)
"""

import os
import json
import logging
from typing import Optional, Dict, Any, List
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq
from ..schemas.extraction import CUFEData, InvoiceLineItemData, CompanyInformation, Address, MeasurementUnit

# Configure logging
logger = logging.getLogger(__name__)

class LLMExtractionService:
    """Service for extracting invoice data using LLM with multi-provider support"""

    def __init__(self):
        """Initialize the LLM extraction service with LangChain"""
        self.provider = os.getenv("LLM_PROVIDER", "openai").lower()
        self.model_name = os.getenv("LLM_MODEL", "gpt-4o-mini")

        # Initialize the appropriate LLM based on provider
        if self.provider == "groq":
            self.llm = ChatGroq(
                groq_api_key=os.getenv("GROQ_API_KEY", "your-groq-api-key-here"),
                model_name=os.getenv("GROQ_MODEL", "llama-3.1-70b-versatile"),
                temperature=0.1,
                max_tokens=4000
            )
            logger.info(f"Initialized Groq LLM with model: {os.getenv('GROQ_MODEL', 'llama-3.1-70b-versatile')}")
        else:  # Default to OpenAI
            self.llm = ChatOpenAI(
                openai_api_key=os.getenv("OPENAI_API_KEY", "your-openai-api-key-here"),
                model_name=self.model_name,
                temperature=0.1,
                max_tokens=4000
            )
            logger.info(f"Initialized OpenAI LLM with model: {self.model_name}")

        # Initialize JSON output parser
        self.json_parser = JsonOutputParser()
    
    def extract_invoice_data(self, xml_content: str) -> Optional[CUFEData]:
        """
        Extract invoice data from XML using LLM with LangChain

        Args:
            xml_content: Raw XML content of the invoice

        Returns:
            CUFEData object with extracted information or None if extraction fails
        """
        try:
            # Create the extraction prompt
            user_prompt = self._create_extraction_prompt(xml_content)

            # Create messages for LangChain
            messages = [
                SystemMessage(content="You are an expert in extracting data from Colombian electronic invoices (UBL 2.1 XML format). Extract all relevant information and return it as valid JSON. Return ONLY the JSON object, no additional text or explanations."),
                HumanMessage(content=user_prompt)
            ]

            # Call LLM using LangChain
            logger.info(f"Calling {self.provider} LLM for extraction...")
            response = self.llm.invoke(messages)

            # Parse the JSON response
            extracted_data = self.json_parser.parse(response.content)

            # Convert to CUFEData object
            result = self._convert_to_cufe_data(extracted_data)

            if result and result.cufe_value:
                logger.info(f"LLM extraction successful using {self.provider}")
                return result
            else:
                logger.warning(f"LLM extraction returned no CUFE using {self.provider}")
                return None

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed: {str(e)}")
            logger.error(f"Raw response: {response.content if 'response' in locals() else 'No response'}")
            return None
        except Exception as e:
            logger.error(f"LLM extraction failed with {self.provider}: {str(e)}")
            return None
    
    def _create_extraction_prompt(self, xml_content: str) -> str:
        """Create the extraction prompt for the LLM"""
        
        return f"""
Extract all relevant information from this Colombian electronic invoice XML and return it as JSON.

XML Content:
{xml_content}

Please extract the following information and return it as a JSON object with this exact structure:

{{
    "cufe_value": "The CUFE (Código Único de Facturación Electrónica) - look for UUID with schemeName='CUFE'",
    "document_number": "Invoice document number",
    "issue_date": "Invoice issue date (YYYY-MM-DD format)",
    "total_amount": "Total amount including taxes (as string)",
    "tax_exclusive_amount": "Amount before taxes (as string)",
    "tax_inclusive_amount": "Amount including taxes (as string)",
    "iva_amount": "Total IVA/VAT amount (as string)",
    "currency_code": "Currency code (usually COP)",
    "invoice_type_code": "Invoice type code",
    "issuer_company": {{
        "business_name": "Issuer company name",
        "tax_id": "Tax identification number (NIT)",
        "address": {{
            "street": "Street address",
            "city": "City",
            "state": "State/Department",
            "postal_code": "Postal code",
            "country": "Country code"
        }},
        "phone": "Phone number",
        "email": "Email address"
    }},
    "customer_company": {{
        "business_name": "Customer company name",
        "tax_id": "Customer tax ID",
        "address": {{
            "street": "Customer street address",
            "city": "Customer city",
            "state": "Customer state",
            "postal_code": "Customer postal code",
            "country": "Customer country"
        }},
        "phone": "Customer phone",
        "email": "Customer email"
    }},
    "line_items": [
        {{
            "line_number": "Line item number",
            "item_name": "Product/service name",
            "item_description": "Product/service description",
            "item_code": "Product code if available",
            "invoiced_quantity": "Quantity (as string)",
            "unit_of_measure": "Unit of measure (EA, KG, HUR, etc.)",
            "unit_price": "Unit price (as string)",
            "line_extension_amount": "Line total before tax (as string)",
            "line_tax_amount": "Tax amount for this line (as string)",
            "line_tax_inclusive_amount": "Line total including tax (as string)",
            "tax_category_code": "Tax category code",
            "vat_rate": "VAT rate percentage (as string)",
            "subtotal_without_vat": "Subtotal without VAT (as string)",
            "vat_amount_per_unit": "VAT amount per unit (as string)"
        }}
    ]
}}

Important notes:
1. All monetary amounts should be strings without currency symbols
2. Dates should be in YYYY-MM-DD format
3. If a field is not found, use null
4. For unit_of_measure, use standard codes like: EA (each), KG (kilogram), HUR (hour), L (liter), M (meter), etc.
5. Extract ALL line items from the invoice
6. Calculate subtotal_without_vat and vat_amount_per_unit for each line item
7. Look for CUFE in elements like <cbc:UUID schemeName="CUFE">
8. Extract complete company information including addresses
9. Return only valid JSON, no additional text or explanations
"""
    
    def _convert_to_cufe_data(self, extracted_data: Dict[str, Any]) -> CUFEData:
        """Convert extracted JSON data to CUFEData object"""
        
        # Convert line items
        line_items = []
        if extracted_data.get("line_items"):
            for item_data in extracted_data["line_items"]:
                # Map unit of measure to enum
                unit_measure = self._map_unit_of_measure(item_data.get("unit_of_measure"))
                
                line_item = InvoiceLineItemData(
                    line_number=item_data.get("line_number"),
                    item_name=item_data.get("item_name"),
                    item_description=item_data.get("item_description"),
                    item_code=item_data.get("item_code"),
                    invoiced_quantity=item_data.get("invoiced_quantity"),
                    unit_of_measure=unit_measure,
                    unit_price=item_data.get("unit_price"),
                    line_extension_amount=item_data.get("line_extension_amount"),
                    line_tax_amount=item_data.get("line_tax_amount"),
                    line_tax_inclusive_amount=item_data.get("line_tax_inclusive_amount"),
                    tax_category_code=item_data.get("tax_category_code"),
                    vat_rate=item_data.get("vat_rate"),
                    subtotal_without_vat=item_data.get("subtotal_without_vat"),
                    vat_amount_per_unit=item_data.get("vat_amount_per_unit")
                )
                line_items.append(line_item)
        
        # Convert company information
        issuer_company = None
        if extracted_data.get("issuer_company"):
            issuer_data = extracted_data["issuer_company"]
            address_data = issuer_data.get("address", {})
            
            address = Address(
                street=address_data.get("street"),
                city=address_data.get("city"),
                state=address_data.get("state"),
                postal_code=address_data.get("postal_code"),
                country=address_data.get("country")
            )
            
            issuer_company = CompanyInformation(
                business_name=issuer_data.get("business_name"),
                tax_id=issuer_data.get("tax_id"),
                address=address,
                phone=issuer_data.get("phone"),
                email=issuer_data.get("email")
            )
        
        # Convert customer company
        customer_company = None
        if extracted_data.get("customer_company"):
            customer_data = extracted_data["customer_company"]
            address_data = customer_data.get("address", {})
            
            address = Address(
                street=address_data.get("street"),
                city=address_data.get("city"),
                state=address_data.get("state"),
                postal_code=address_data.get("postal_code"),
                country=address_data.get("country")
            )
            
            customer_company = CompanyInformation(
                business_name=customer_data.get("business_name"),
                tax_id=customer_data.get("tax_id"),
                address=address,
                phone=customer_data.get("phone"),
                email=customer_data.get("email")
            )
        
        # Create CUFEData object
        return CUFEData(
            cufe_value=extracted_data.get("cufe_value"),
            document_number=extracted_data.get("document_number"),
            issue_date=extracted_data.get("issue_date"),
            total_amount=extracted_data.get("total_amount"),
            tax_exclusive_amount=extracted_data.get("tax_exclusive_amount"),
            tax_inclusive_amount=extracted_data.get("tax_inclusive_amount"),
            iva_amount=extracted_data.get("iva_amount"),
            currency_code=extracted_data.get("currency_code"),
            invoice_type_code=extracted_data.get("invoice_type_code"),
            line_items=line_items,
            issuer_company=issuer_company,
            customer_company=customer_company
        )
    
    def _map_unit_of_measure(self, unit_str: Optional[str]) -> MeasurementUnit:
        """Map unit string to MeasurementUnit enum"""
        if not unit_str:
            return MeasurementUnit.UNKNOWN
        
        unit_mapping = {
            "EA": MeasurementUnit.EACH,
            "KG": MeasurementUnit.KILOGRAM,
            "HUR": MeasurementUnit.HOUR,
            "L": MeasurementUnit.LITER,
            "M": MeasurementUnit.METER,
            "M2": MeasurementUnit.SQUARE_METER,
            "M3": MeasurementUnit.CUBIC_METER,
            "TON": MeasurementUnit.METRIC_TON,
            "G": MeasurementUnit.GRAM,
            "ML": MeasurementUnit.MILLILITER,
            "CM": MeasurementUnit.CENTIMETER,
            "MM": MeasurementUnit.MILLIMETER,
            "KM": MeasurementUnit.KILOMETER,
            "MIN": MeasurementUnit.MINUTE,
            "SEC": MeasurementUnit.SECOND,
            "DAY": MeasurementUnit.DAY,
            "WEEK": MeasurementUnit.WEEK,
            "MONTH": MeasurementUnit.MONTH,
            "YEAR": MeasurementUnit.YEAR,
            "PIECE": MeasurementUnit.PIECE,
            "PACK": MeasurementUnit.PACKAGE,
            "BOX": MeasurementUnit.BOX,
            "BOTTLE": MeasurementUnit.BOTTLE,
            "BAG": MeasurementUnit.BAG,
            "ROLL": MeasurementUnit.ROLL,
            "SHEET": MeasurementUnit.SHEET,
            "SET": MeasurementUnit.SET,
            "PAIR": MeasurementUnit.PAIR,
            "DOZEN": MeasurementUnit.DOZEN
        }
        
        return unit_mapping.get(unit_str.upper(), MeasurementUnit.UNKNOWN)
